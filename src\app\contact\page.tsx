"use client";

import React, { useState } from 'react';
import Header from '@/components/Header';
import styles from './page.module.css';

export default function ContactPage() {
  const [submitting, setSubmitting] = useState(false);
  const [status, setStatus] = useState<string | null>(null);

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (submitting) return;
    setSubmitting(true);
    setStatus(null);
    try {
      const form = e.currentTarget;
      const formData = new FormData(form);
      const payload = {
        name: formData.get('name'),
        email: formData.get('email'),
        message: formData.get('message')
      } as { name: string; email: string; message: string };

      const res = await fetch('/api/contact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (!res.ok) {
        const data = await res.json().catch(() => ({}));
        throw new Error(data.error || 'Eroare la trimiterea mesajului.');
      }

      setStatus('Mesajul a fost trimis cu succes. Vă vom contacta cât mai curând.');
      form.reset();
    } catch (err: any) {
      setStatus(err?.message || 'Eroare la trimiterea mesajului.');
    } finally {
      setSubmitting(false);
    }
  }

  return (
    <>
      <Header />
      <main className={styles.container}>
        <h1 className={styles.title}>Contact</h1>
        <p className={styles.updated}>EMD Creative Media SRL • <EMAIL> • Teleorman, Turnu Magurele, Argeș 74</p>

        <form className={styles.form} onSubmit={handleSubmit}>
          <div className={styles.fieldGroup}>
            <label htmlFor="name">Nume</label>
            <input id="name" name="name" type="text" required placeholder="Numele dvs." />
          </div>
          <div className={styles.fieldGroup}>
            <label htmlFor="email">Email</label>
            <input id="email" name="email" type="email" required placeholder="<EMAIL>" />
          </div>
          <div className={styles.fieldGroup}>
            <label htmlFor="message">Mesaj</label>
            <textarea id="message" name="message" rows={6} required placeholder="Scrieți mesajul aici..." />
          </div>
          <button className={styles.submitButton} type="submit" disabled={submitting}>
            {submitting ? 'Se trimite...' : 'Trimite'}
          </button>
          {status && <p className={styles.status}>{status}</p>}
        </form>
      </main>
      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerLinksSimple}>
            <a href="/privacy">Politica de Confidențialitate</a>
            <span className={styles.footerDivider}>•</span>
            <a href="/terms">Termeni și Condiții</a>
          </div>
        </div>
      </footer>
    </>
  );
}




