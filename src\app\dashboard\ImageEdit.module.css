.imageEditCard {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.imageEditCard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.imageEditLeft {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1;
}

.imageEditImages {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.imageEditImageContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 200px;
}

.imageEditImage {
  width: 100%;
  height: auto;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e5e7eb;
}

.imageEditImageLabel {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-align: center;
}

.imageEditInfoBlock {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.imageEditInfo {
  font-size: 14px;
  color: #374151;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.imageEditInfo strong {
  font-weight: 600;
  color: #111827;
  min-width: 80px;
  flex-shrink: 0;
}

.imageEditInfo span {
  flex: 1;
  word-break: break-word;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status.processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.status.completed {
  background-color: #d1fae5;
  color: #065f46;
}

.status.failed {
  background-color: #fee2e2;
  color: #991b1b;
}

.imageEditButtonContainer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.imageEditButton {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .imageEditCard {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .imageEditLeft {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .imageEditImages {
    align-self: center;
    gap: 16px;
  }

  .imageEditImageContainer {
    min-width: 150px;
  }

  .imageEditButtonContainer {
    justify-content: center;
  }

  .imageEditButton {
    width: 100%;
    align-self: stretch;
  }
}
