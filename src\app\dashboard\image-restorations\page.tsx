"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';
import Header from '@/components/Header';
import Button from 'react-bootstrap/Button';
import ImageRestoration from '../ImageRestoration';
import { useTranslation } from '@/hooks/useTranslation';
import styles from '../image-edits/page.module.css'; // Reuse the same styles

export default function ImageRestorationsPage() {
  const router = useRouter();
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [imageRestorations, setImageRestorations] = useState<any[]>([]);
  const [imageRestorationsLoading, setImageRestorationsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Always use Romanian translations
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  const ITEMS_PER_PAGE = 10;

  useEffect(() => {
    async function checkSession() {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/login');
      } else {
        setSession(session);
        setLoading(false);
      }
    }
    checkSession();
  }, [router]);

  useEffect(() => {
    async function fetchImageRestorations() {
      if (!session?.user?.id) {
        setImageRestorationsLoading(false);
        return;
      }

      setImageRestorationsLoading(true);

      // First get the total count
      const { count, error: countError } = await supabase
        .from('web_image_restorations')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', session.user.id);

      if (!countError && count !== null) {
        setTotalCount(count);
        setHasMore(count > currentPage * ITEMS_PER_PAGE);
      }

      // Load image restoration data including image URLs for display
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;
      const { data, error } = await supabase
        .from('web_image_restorations')
        .select(`
          id,
          status,
          token_cost,
          created_at,
          completed_at,
          error_message,
          original_image_url,
          restored_image_url
        `)
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + ITEMS_PER_PAGE - 1);

      if (!error && data) {
        setImageRestorations(data);
      } else {
        console.error('Error fetching image restorations:', error);
      }
      setImageRestorationsLoading(false);
    }
    fetchImageRestorations();
  }, [session, currentPage, ITEMS_PER_PAGE]);

  const handleViewDetails = (id: string) => {
    // Navigate to image restoration with the specific restoration ID
    router.push(`/image-restoration?id=${id}`);
  };

  const handleConvertToVideo = (imageUrl: string) => {
    // Navigate to image-to-video page with the restored image URL
    router.push(`/image-to-video?imageUrl=${encodeURIComponent(imageUrl)}`);
  };

  return (
    <>
      <Header />
      <main className={styles.mainContainer}>
        {loading ? (
          <div>Loading...</div>
        ) : (
          <>
            <div className={styles.headerSection}>
              <div className={styles.titleContainer}>
                <h1 className={styles.pageTitle}>🔧 Image Restorations</h1>
                {!imageRestorationsLoading && (
                  <span className={styles.counter}>
                    {imageRestorations.length} restoration{imageRestorations.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
              <Button variant="outline-secondary" onClick={() => router.push('/dashboard')}>
                Back to Dashboard
              </Button>
            </div>

            <div className={styles.subtitleSection}>
              <p className={styles.subtitle}>
                View and manage all your image restorations. Automatically repair defects and colorize your photos.
              </p>
            </div>

            {imageRestorationsLoading ? (
              <div className={styles.loadingContainer}>
                <div className={styles.spinner}></div>
                Loading restorations...
              </div>
            ) : imageRestorations.length === 0 ? (
              <div className={styles.emptyState}>
                <div className={styles.emptyStateIcon}>🔧</div>
                <div className={styles.emptyStateTitle}>No Image Restorations Yet</div>
                <div>Start restoring your damaged or old photos with AI-powered restoration.</div>
                <Button
                  variant="primary"
                  className={styles.startEditingButton}
                  onClick={() => router.push('/image-restoration')}
                >
                  Start Restoring Images
                </Button>
              </div>
            ) : (
              <>
                <div className={styles.editsContainer}>
                  {imageRestorations.map(restoration => (
                    <ImageRestoration
                      key={restoration.id}
                      restoration={restoration}
                      t={t}
                      onViewDetails={handleViewDetails}
                      onConvertToVideo={handleConvertToVideo}
                    />
                  ))}
                </div>

                {/* Pagination Controls */}
                {totalCount > ITEMS_PER_PAGE && (
                  <div className={styles.paginationContainer}>
                    <Button
                      variant="outline-secondary"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(currentPage - 1)}
                    >
                      Previous
                    </Button>

                    <span className={styles.paginationInfo}>
                      Page {currentPage} of {Math.ceil(totalCount / ITEMS_PER_PAGE)} 
                      (showing {imageRestorations.length} of {totalCount} restorations)
                    </span>

                    <Button
                      variant="outline-secondary"
                      disabled={!hasMore}
                      onClick={() => setCurrentPage(currentPage + 1)}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </main>
    </>
  );
}
