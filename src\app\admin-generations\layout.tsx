"use client";

import React from "react";
import Header from "@/components/Header";
import Link from "next/link";
import { usePathname } from "next/navigation";
import styles from "../admin/admin.module.css";

export default function AdminGenerationsLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  return (
    <>
      <Header />
      <main className={styles.container}>
        <h1>Admin Generations Management</h1>
        <nav className={styles.adminNav}>
          <Link 
            href="/admin-generations/generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/generations' ? styles.activeLink : ''}`}
          >
            Telegram generations
          </Link>
          <Link 
            href="/admin-generations/mk-generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/mk-generations' ? styles.activeLink : ''}`}
          >
            Mortal Kombat generations
          </Link>
          <Link 
            href="/admin-generations/video-generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/video-generations' ? styles.activeLink : ''}`}
          >
            Multi Step Requests
          </Link>
          <Link 
            href="/admin-generations/video-requests"
            className={`${styles.navLink} ${pathname === '/admin-generations/video-requests' ? styles.activeLink : ''}`}
          >
            Web Video Generations
          </Link>
          <Link 
            href="/admin-generations/image-generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/image-generations' ? styles.activeLink : ''}`}
          >
            Web Image Generations
          </Link>
          <Link 
            href="/admin-generations/web-image-edits"
            className={`${styles.navLink} ${pathname === '/admin-generations/web-image-edits' ? styles.activeLink : ''}`}
          >
            Web Image Edits
          </Link>
          <Link 
            href="/admin-generations/logo-generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/logo-generations' ? styles.activeLink : ''}`}
          >
            Logo Generations
          </Link>
          <Link
            href="/admin-generations/free-generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/free-generations' ? styles.activeLink : ''}`}
          >
            Free Generations
          </Link>
          <Link
            href="/admin-generations/image-to-video-generations"
            className={`${styles.navLink} ${pathname === '/admin-generations/image-to-video-generations' ? styles.activeLink : ''}`}
          >
            Image to Video Generations
          </Link>
          <Link
            href="/admin-generations/image-generator-settings"
            className={`${styles.navLink} ${pathname === '/admin-generations/image-generator-settings' ? styles.activeLink : ''}`}
          >
            Generator Settings
          </Link>
        </nav>
        <div className={styles.adminContent}>
          {children}
        </div>
      </main>
    </>
  );
} 