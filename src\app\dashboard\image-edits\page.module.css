.mainContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  min-height: calc(100vh - 80px);
}

.headerSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 12px;
}

.pageTitle {
  font-size: 32px;
  font-weight: 700;
  margin: 0;
}

.counter {
  font-size: 18px;
  color: #6b7280;
  font-weight: 500;
}

.subtitleSection {
  margin-bottom: 24px;
}

.subtitle {
  font-size: 16px;
  color: #6b7280;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 48px 24px;
  color: #6b7280;
  font-size: 16px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
  font-size: 16px;
}

.emptyStateIcon {
  font-size: 48px;
  margin-bottom: 16px;
}

.emptyStateTitle {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 8px;
}

.startEditingButton {
  margin-top: 24px;
}

.editsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.paginationContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 32px;
  padding: 16px 0;
}

.paginationInfo {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}
