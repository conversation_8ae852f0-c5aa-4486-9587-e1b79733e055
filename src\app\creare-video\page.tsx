'use client';

import React, { useEffect, useState, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import styles from './page.module.css';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchUserProfile, logout } from '../../store/userSlice';

export default function CreareVideoPage() {
  const dispatch = useDispatch<AppDispatch>();
  const { user, loading, error } = useSelector((state: RootState) => state.user);
  const [menuOpen, setMenuOpen] = useState(false);
  const [localLoading, setLocalLoading] = useState(true);
  const hasAttemptedFetch = useRef(false);

  useEffect(() => {
    // Only fetch profile once when component mounts
    if (!hasAttemptedFetch.current) {
      hasAttemptedFetch.current = true;
      dispatch(fetchUserProfile());
    }

    // Update local loading state based on Redux state
    if (!loading) {
      setLocalLoading(false);
    }
  }, [dispatch, loading]);

  // Prevent scrolling when mobile menu is open
  useEffect(() => {
    if (menuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    
    return () => {
      document.body.style.overflow = '';
    };
  }, [menuOpen]);

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  return (
    <div className={styles.container}>
      <header className={styles.header}>
        <Link href="/" className={styles.logo}>
          <Image 
            src="/aivis-wide-small.png" 
            alt="Logo Aivis" 
            width={130} 
            height={40} 
          />
        </Link>
        
        <button 
          className={`${styles.hamburger} ${menuOpen ? styles.active : ''}`} 
          onClick={toggleMenu}
          aria-label="Toggle navigation menu"
        >
          <span className={styles.hamburgerLine}></span>
          <span className={styles.hamburgerLine}></span>
          <span className={styles.hamburgerLine}></span>
        </button>
        
        <nav className={`${styles.nav} ${menuOpen ? styles.menuOpen : ''}`}>
          {localLoading ? null : user ? (
            <>
              <Link href="/dashboard" className={styles.navLink} onClick={() => setMenuOpen(false)}>
                Dashboard
              </Link>
              <button 
                onClick={() => {
                  setMenuOpen(false);
                  dispatch(logout());
                }} 
                className={styles.navButton}
              >
                Deconectare
              </button>
            </>
          ) : error ? (
            <>
              <span className={styles.errorText}>Eroare autentificare</span>
              <button 
                onClick={() => {
                  setMenuOpen(false);
                  dispatch(logout());
                }} 
                className={styles.navButton}
              >
                Deconectare
              </button>
            </>
          ) : (
            <>
              <Link href="/register-coming-soon" className={styles.navLink} onClick={() => setMenuOpen(false)}>
                Înregistrare
              </Link>
              <Link href="/login" className={styles.navLink} onClick={() => setMenuOpen(false)}>
                Autentificare
              </Link>
            </>
          )}
        </nav>
      </header>

      <div className={styles.hero}>
        <h1 className={styles.title}>Creare Video</h1>
        <p className={styles.subtitle}>
          Transformă imaginile tale în videoclipuri captivante cu ajutorul inteligenței artificiale
        </p>
      </div>

      <div className={styles.content}>
        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>Ce poți face cu serviciul nostru?</h2>
          <div className={styles.features}>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>🎬</div>
              <h3>Conversie imagine-video</h3>
              <p>Transformă orice imagine statică într-un videoclip dinamic și captivant</p>
            </div>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>⚡</div>
              <h3>Efecte dinamice</h3>
              <p>Adaugă mișcare naturală și efecte vizuale impresionante imaginilor tale</p>
            </div>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>🎨</div>
              <h3>Animații personalizate</h3>
              <p>Creează animații unice bazate pe conținutul și stilul imaginii tale</p>
            </div>
            <div className={styles.feature}>
              <div className={styles.featureIcon}>📱</div>
              <h3>Multiple formate</h3>
              <p>Exportă videoclipurile în diferite rezoluții și formate pentru orice platformă</p>
            </div>
          </div>
        </div>

        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>Cum funcționează?</h2>
          <div className={styles.steps}>
            <div className={styles.step}>
              <div className={styles.stepNumber}>1</div>
              <div className={styles.stepContent}>
                <h3>Încarcă imaginea</h3>
                <p>Selectează imaginea pe care vrei să o transformi în video. Acceptăm formate JPG, PNG și WEBP.</p>
              </div>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>2</div>
              <div className={styles.stepContent}>
                <h3>Descrie mișcarea dorită</h3>
                <p>Scrie un prompt care descrie cum vrei să se miște imaginea. De exemplu: "pantera aleargă pe plajă" sau "frunzele se mișcă în vânt".</p>
              </div>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>3</div>
              <div className={styles.stepContent}>
                <h3>Alege setările</h3>
                <p>Selectează durata videoclipului (3-10 secunde) și rezoluția dorită (360p până la 1080p).</p>
              </div>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>4</div>
              <div className={styles.stepContent}>
                <h3>Generează și descarcă</h3>
                <p>IA-ul nostru va procesa imaginea și va crea videoclipul în aproximativ 2-5 minute. Apoi îl poți descărca.</p>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.section}>
          <h2 className={styles.sectionTitle}>Exemple de utilizare</h2>
          <div className={styles.examples}>
            <div className={styles.example}>
              <h4>🏞️ Peisaje animate</h4>
              <p>Transformă fotografiile de natură în scene vii cu apa care curge, nori care se mișcă sau frunze care dansează în vânt.</p>
            </div>
            <div className={styles.example}>
              <h4>👥 Portrete dinamice</h4>
              <p>Adaugă mișcare subtilă portretelor - părul care se mișcă, ochii care clipesc sau un zâmbet care se formează.</p>
            </div>
            <div className={styles.example}>
              <h4>🎨 Artă digitală</h4>
              <p>Dă viață operelor de artă digitală cu efecte de particule, schimbări de lumină sau transformări graduale.</p>
            </div>
            <div className={styles.example}>
              <h4>📱 Conținut social media</h4>
              <p>Creează conținut captivant pentru Instagram, TikTok sau Facebook din imaginile tale statice.</p>
            </div>
          </div>
        </div>


        <div className={styles.cta}>
          <h2>Gata să începi?</h2>
          <p>Transformă imaginile tale în videoclipuri spectaculoase chiar acum!</p>
          <Link href="/image-to-video" className={styles.ctaButton}>
            Începe crearea video
          </Link>
        </div>
      </div>

      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerLogo}>
            <Image
              src="/aivis-wide-small.png"
              alt="Logo Aivis"
              width={150}
              height={42}
            />
            <p>© 2024 Aivis. Toate drepturile rezervate.</p>
          </div>
          <div className={styles.footerLinks}>
            <div className={styles.footerLinkColumn}>
              <h4>Servicii</h4>
              <Link href="/services/logo-creation">Creare Logo</Link>
              <Link href="/services/image-generation">Generare Imagini</Link>
              <Link href="/services/image-editing">Editare Imagini</Link>
              <Link href="/creare-video">Creare Video</Link>
            </div>
            <div className={styles.footerLinkColumn}>
              <h4>Companie</h4>
              <Link href="/about">Despre Noi</Link>
              <Link href="/contact">Contact</Link>
              <Link href="/blog">Blog</Link>
            </div>
            <div className={styles.footerLinkColumn}>
              <h4>Legal</h4>
              <Link href="/terms">Termeni și Condiții</Link>
              <Link href="/privacy">Politica de Confidențialitate</Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
