"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "../store";
import { fetchUserProfile, clearUser } from "../store/userSlice";
import { PersonCircle } from "react-bootstrap-icons";
import { FaCoins } from "react-icons/fa";
import { supabase } from "../lib/supabaseClient";
import Link from "next/link";
import Image from "next/image";
import styles from "./Header.module.css";
import { useTranslation } from "@/hooks/useTranslation";
import { logger } from '../utils/logger';

interface HeaderProps {
  isFreeGeneration?: boolean;
}

const Header: React.FC<HeaderProps> = ({ isFreeGeneration = false }) => {
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.user);
  const [profileMenuShown, setProfileMenuShown] = React.useState(false);
  const [walletBalance, setWalletBalance] = useState<number | null>(null);
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  // If no user data is loaded yet, dispatch the fetch action (skip for free generations).
  useEffect(() => {
    if (!isFreeGeneration && !user) {
      dispatch(fetchUserProfile());
    }
  }, [user, dispatch, isFreeGeneration]);

  useEffect(() => {
    if (isFreeGeneration) return; // Skip wallet balance for free generations
    
    async function fetchWalletBalance() {
      if (!user) return;
      const { data: wallet, error } = await supabase
        .from('wallets')
        .select('balance')
        .eq('user_id', user.id)
        .maybeSingle();
      if (!error && wallet) {
        setWalletBalance(wallet.balance);
      }
    }
    fetchWalletBalance();
  }, [user, isFreeGeneration]);

  const destination = user ? "/dashboard" : "/";

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      logger.error("Logout error:", error.message);
    } else {
      dispatch(clearUser());
      router.push("/login");
    }
  };

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <div className={styles.logo}>
          <Link href={destination}>
            <Image 
              src="/aivis-wide-small.png" 
              alt="PodVideos Logo" 
              width={100} 
              height={0}
              style={{ height: 'auto' }}
              priority
            />
          </Link>
        </div>
        <nav className={styles.nav}>
        </nav>
        
        <div className={styles.userSection}>
          {isFreeGeneration ? (
            <div className={styles.freeGenerationText}>
              <span>Generare gratuită</span>
            </div>
          ) : user ? (
            <>
              <div className={styles.tokenContainer}>
                <FaCoins size={24} color="black" />
                <span className={styles.tokenCount}>{walletBalance !== null ? walletBalance : '...'}</span>
              </div>
              
              <div className={styles.profileContainer}>
                <button
                  onClick={() => setProfileMenuShown((prev) => !prev)}
                  className={styles.profileButton}
                >
                  <PersonCircle size={24} />
                </button>
                
                {profileMenuShown && (
                  <div className={styles.profileMenu}>
                    <div className={styles.menuSection}>
                      <div className={styles.menuSectionTitle}>🛠️ {t('menu_tools')}</div>
                      <button
                        onClick={() => {
                          setProfileMenuShown(false);
                          router.push("/tools");
                        }}
                        className={styles.menuItem}
                      >
                        📋 {t('menu_view_all_tools')}
                      </button>
                      <button
                        onClick={() => {
                          setProfileMenuShown(false);
                          router.push("/mortal-kombat-video");
                        }}
                        className={styles.menuItem}
                      >
                        ⚔️ {t('menu_mortal_kombat_video')}
                      </button>
                      <button
                        onClick={() => {
                          setProfileMenuShown(false);
                          router.push("/image-editor");
                        }}
                        className={styles.menuItem}
                      >
                        🎨 {t('menu_ai_image_editor')}
                      </button>
                      <button
                        onClick={() => {
                          setProfileMenuShown(false);
                          router.push("/image-restoration");
                        }}
                        className={styles.menuItem}
                      >
                        🔧 Image Restoration
                      </button>
                    </div>
                    <div className={styles.menuDivider}></div>
                    {user?.role === "admin" && (
                      <>
                        <div className={styles.menuSection}>
                          <div className={styles.menuSectionTitle}>⚙️ {t('menu_admin')}</div>
                          <button
                            onClick={() => {
                              setProfileMenuShown(false);
                              router.push("/admin");
                            }}
                            className={styles.menuItem}
                          >
                            📊 {t('menu_admin_dashboard')}
                          </button>
                          <button
                            onClick={() => {
                              setProfileMenuShown(false);
                              router.push("/admin-generations");
                            }}
                            className={styles.menuItem}
                          >
                            🧩 Admin Generations Management
                          </button>
                          <button
                            onClick={() => {
                              setProfileMenuShown(false);
                              router.push("/admin/image-generations");
                            }}
                            className={styles.menuItem}
                          >
                            🎨 {t('menu_web_image_generations')}
                          </button>
                          <button
                            onClick={() => {
                              setProfileMenuShown(false);
                              router.push("/admin/video-generations");
                            }}
                            className={styles.menuItem}
                          >
                            📋 {t('menu_multi_step_requests')}
                          </button>
                          <button
                            onClick={() => {
                              setProfileMenuShown(false);
                              router.push("/admin/video-requests");
                            }}
                            className={styles.menuItem}
                          >
                            🎬 {t('menu_web_video_generations')}
                          </button>
                        </div>
                        <div className={styles.menuDivider}></div>
                      </>
                    )}
                    <button
                      onClick={() => {
                        setProfileMenuShown(false);
                        router.push("/dashboard/tokens");
                      }}
                      className={styles.menuItem}
                    >
                      💰 {t('menu_credits')}
                    </button>
                    <button
                      onClick={() => {
                        setProfileMenuShown(false);
                        router.push("/profile");
                      }}
                      className={styles.menuItem}
                    >
                      👤 {t('menu_view_profile')}
                    </button>
                    <button
                      onClick={() => {
                        setProfileMenuShown(false);
                        handleLogout();
                      }}
                      className={styles.menuItem}
                    >
                      🚪 {t('menu_logout')}
                    </button>
                  </div>
                )}
              </div>
            </>
          ) : (
            <div className={styles.loadingText}>Loading...</div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header; 