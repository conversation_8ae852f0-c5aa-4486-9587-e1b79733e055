import { createClient } from '@supabase/supabase-js';
import * as fal from "@fal-ai/serverless-client";

// Configure FAL client
fal.config({
  credentials: process.env.FAL_API_KEY || '',
});

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

interface RestorationRequest {
  id: string;
  user_id: string;
  wallet_id: string;
  status: string;
  original_image_url: string;
  output_format: string;
  seed?: number;
  token_cost: number;
}

export async function processImageRestorationAsync(restorationId: string): Promise<void> {
  try {
    console.log(`Starting restoration processing for ID: ${restorationId}`);

    // Get the restoration request from database
    const { data: restorationRequest, error: fetchError } = await supabaseAdmin
      .from('web_image_restorations')
      .select('*')
      .eq('id', restorationId)
      .single();

    if (fetchError || !restorationRequest) {
      console.error('Error fetching restoration request:', fetchError);
      return;
    }

    const request = restorationRequest as RestorationRequest;

    // Update status to processing
    await supabaseAdmin
      .from('web_image_restorations')
      .update({ 
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', restorationId);

    console.log('Submitting restoration request to nano-banana model...');

    // Submit the request to FAL API using nano-banana model for restoration
    // Note: For restoration, we don't provide a prompt - the model automatically
    // repairs defects and colorizes the image
    const result = await fal.subscribe("fal-ai/nano-banana/edit", {
      input: {
        // For restoration, we can use a generic restoration prompt or no prompt
        // The nano-banana model is designed to automatically restore images
        prompt: "restore and enhance this image, repair any defects and colorize if needed",
        image_urls: [request.original_image_url.trim()]
      },
      logs: true,
      onQueueUpdate: (update) => {
        if (update.status === "IN_PROGRESS") {
          console.log("Nano Banana Restoration Processing:", update.logs?.map((log) => log.message).join('\n'));
        }
      },
    });

    console.log('FAL API restoration result:', result);

    if (!result.data || !result.data.image) {
      throw new Error('No restored image returned from API');
    }

    const restoredImageUrl = result.data.image.url;
    console.log('Restoration completed successfully:', restoredImageUrl);

    // Update the database with the restored image
    const { error: updateError } = await supabaseAdmin
      .from('web_image_restorations')
      .update({
        status: 'completed',
        restored_image_url: restoredImageUrl,
        fal_request_id: result.requestId,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', restorationId);

    if (updateError) {
      console.error('Error updating restoration record:', updateError);
      throw updateError;
    }

    console.log(`Restoration processing completed successfully for ID: ${restorationId}`);

  } catch (error: any) {
    console.error('Error in restoration processing:', error);

    // Update status to failed and store error message
    await supabaseAdmin
      .from('web_image_restorations')
      .update({
        status: 'failed',
        error_message: error.message || error.toString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', restorationId);

    // Optionally refund tokens on failure
    try {
      const { data: restorationRequest } = await supabaseAdmin
        .from('web_image_restorations')
        .select('wallet_id, token_cost')
        .eq('id', restorationId)
        .single();

      if (restorationRequest) {
        const { data: wallet } = await supabaseAdmin
          .from('wallets')
          .select('tokens')
          .eq('id', restorationRequest.wallet_id)
          .single();

        if (wallet) {
          await supabaseAdmin
            .from('wallets')
            .update({ 
              tokens: wallet.tokens + restorationRequest.token_cost,
              updated_at: new Date().toISOString()
            })
            .eq('id', restorationRequest.wallet_id);

          console.log(`Refunded ${restorationRequest.token_cost} tokens due to restoration failure`);
        }
      }
    } catch (refundError) {
      console.error('Error refunding tokens:', refundError);
    }
  }
}
