.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.updated {
  color: #6b7280;
  margin: 0.5rem 0 2rem 0;
}

.form {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.fieldGroup label {
  font-weight: 600;
  color: #111827;
}

.fieldGroup input,
.fieldGroup textarea {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  padding: 0.75rem 0.9rem;
  font-size: 1rem;
}

.fieldGroup input:focus,
.fieldGroup textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
}

.submitButton {
  background: #2563eb;
  color: #fff;
  border: none;
  padding: 0.8rem 1.2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
}

.submitButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.status {
  margin-top: 0.75rem;
  color: #065f46;
}

.footer {
  border-top: 1px solid #e5e7eb;
  padding: 1rem 0;
  margin-top: 2rem;
  background: #fff;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footerLinksSimple {
  display: flex;
  gap: 0.75rem;
  color: #666;
  font-size: 0.95rem;
}

.footerLinksSimple a {
  color: #4b5563;
  text-decoration: none;
}

.footerLinksSimple a:hover {
  text-decoration: underline;
}

.footerDivider {
  color: #9ca3af;
}













