import { createClient } from '@supabase/supabase-js';
import { fal } from '@fal-ai/client';

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

// Configure FAL client
fal.config({
  credentials: process.env.FAL_API_KEY || ''
});

// Get selected image editor model from database
async function getSelectedImageEditorModel(): Promise<string> {
  const { data, error } = await supabaseAdmin
    .from('config_options')
    .select('value')
    .eq('key', 'image_editor_model')
    .single();

  if (error || !data || !data.value || !data.value.selected) {
    console.log('No image editor model config found, defaulting to flux-pro/kontext');
    return 'flux_context_pro';
  }

  return data.value.selected;
}

// Process the image edit asynchronously
export async function processImageEditAsync(requestId: string) {
  try {
    console.log(`Starting async processing for image edit: ${requestId}`);
    
    // Get the request from database
    const { data: editRequest, error: fetchError } = await supabaseAdmin
      .from('web_image_edits')
      .select('*')
      .eq('id', requestId)
      .single();

    if (fetchError || !editRequest) {
      console.error('Error fetching edit request:', fetchError);
      return;
    }

    // Get the selected model
    const selectedModel = await getSelectedImageEditorModel();

    // Update status to processing
    await supabaseAdmin
      .from('web_image_edits')
      .update({
        status: 'processing',
        model: selectedModel,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    console.log(`Processing image edit with prompt: "${editRequest.prompt}"`);
    console.log(`Using model: ${selectedModel}`);

    // Validate prompt is not empty
    if (!editRequest.prompt || editRequest.prompt.trim().length === 0) {
      throw new Error('Prompt cannot be empty');
    }

    // Validate image URL is not empty
    if (!editRequest.original_image_url || editRequest.original_image_url.trim().length === 0) {
      throw new Error('Image URL cannot be empty');
    }

    // Submit the request to FAL API with the selected model
    let result;
    if (selectedModel === 'nano_banana') {
      // Use Nano Banana model
      result = await fal.subscribe("fal-ai/nano-banana/edit", {
        input: {
          prompt: editRequest.prompt.trim(),
          image_urls: [editRequest.original_image_url.trim()]
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            console.log("Nano Banana Processing:", update.logs?.map((log) => log.message).join('\n'));
          }
        },
      });
    } else {
      // Use Flux Context PRO model (default)
      result = await fal.subscribe("fal-ai/flux-pro/kontext", {
        input: {
          prompt: editRequest.prompt,
          image_url: editRequest.original_image_url,
          guidance_scale: editRequest.guidance_scale,
          num_images: 1,
          safety_tolerance: editRequest.safety_tolerance,
          output_format: editRequest.output_format,
          ...(editRequest.seed && { seed: editRequest.seed })
        },
        logs: true,
        onQueueUpdate: (update) => {
          if (update.status === "IN_PROGRESS") {
            console.log("Flux Pro Processing:", update.logs?.map((log) => log.message).join('\n'));
          }
        },
      });
    }

    console.log('FAL API result:', result);

    if (result.data && result.data.images && result.data.images.length > 0) {
      const editedImageUrl = result.data.images[0].url;
      
      // Update the record with the result
      const { error: updateError } = await supabaseAdmin
        .from('web_image_edits')
        .update({
          status: 'completed',
          edited_image_url: editedImageUrl,
          fal_request_id: result.requestId,
          model: selectedModel,
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      if (updateError) {
        console.error('Error updating edit record:', updateError);
        
        // Update status to failed
        await supabaseAdmin
          .from('web_image_edits')
          .update({
            status: 'failed',
            error_message: 'Failed to update record with result',
            updated_at: new Date().toISOString()
          })
          .eq('id', requestId);
      } else {
        console.log(`Successfully completed image edit: ${requestId}`);
      }
    } else {
      console.error('No images returned from FAL API');
      
      // Update status to failed
      await supabaseAdmin
        .from('web_image_edits')
        .update({
          status: 'failed',
          error_message: 'No images returned from FAL API',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);
    }

  } catch (error: any) {
    console.error('Error processing image edit:', error);
    
    // Update status to failed
    await supabaseAdmin
      .from('web_image_edits')
      .update({
        status: 'failed',
        error_message: error.message || error.toString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);
  }
}
