"use client";

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { supabase } from '../../../lib/supabaseClient';
import styles from '../../admin/admin.module.css';

interface WebImageEdit {
  id: string;
  user_id: string;
  user_email: string;
  user_role: string;
  status: string;
  original_image_url: string;
  edited_image_url: string | null;
  prompt: string;
  guidance_scale: number;
  safety_tolerance: string;
  output_format: string;
  seed?: number;
  token_cost: number;
  is_public: boolean;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

interface WebImageEditsData {
  imageEdits: WebImageEdit[];
  stats: {
    total_count: number;
    completed_count: number;
    pending_count: number;
    processing_count: number;
    failed_count: number;
    public_count: number;
    total_tokens: number;
  };
}

export default function AdminWebImageEdits() {
  const { user } = useSelector((state: RootState) => state.user);
  const [data, setData] = useState<WebImageEditsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchWebImageEdits();
  }, []);

  const fetchWebImageEdits = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/admin/web-image-edits', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err: any) {
      console.error('Error fetching web image edits:', err);
      setError(err.message || 'Failed to fetch web image edits');
    } finally {
      setLoading(false);
    }
  };

  const togglePublicStatus = async (editId: string, currentStatus: boolean) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch(`/api/admin/web-image-edits/${editId}/toggle-public`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_public: !currentStatus
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update public status`);
      }

      // Refresh the data
      fetchWebImageEdits();
    } catch (err: any) {
      console.error('Error toggling public status:', err);
      setError(err.message || 'Failed to update public status');
      setTimeout(() => setError(null), 5000);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'processing': return '#007bff';
      case 'pending': return '#ffc107';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'processing': return '⏳';
      case 'pending': return '🕐';
      case 'failed': return '❌';
      default: return '❓';
    }
  };

  if (user?.role !== 'admin') {
    return (
      <div className={styles.errorMessage}>
        <h2>Access Denied</h2>
        <p>Admin access required to view this page.</p>
      </div>
    );
  }

  if (loading) {
    return <div className={styles.loading}>Loading web image edits...</div>;
  }

  if (error) {
    return (
      <div className={styles.errorMessage}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={fetchWebImageEdits} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return <div className={styles.errorMessage}>No data available</div>;
  }

  return (
    <div className={styles.adminPage}>
      <h2>Web Image Edits Management</h2>
      
      {/* Statistics Summary */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3>Total Edits</h3>
          <div className={styles.statNumber}>{data.stats.total_count}</div>
        </div>
        <div className={styles.statCard}>
          <h3>Completed</h3>
          <div className={styles.statNumber} style={{ color: '#28a745' }}>
            {data.stats.completed_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Processing</h3>
          <div className={styles.statNumber} style={{ color: '#007bff' }}>
            {data.stats.processing_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Pending</h3>
          <div className={styles.statNumber} style={{ color: '#ffc107' }}>
            {data.stats.pending_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Failed</h3>
          <div className={styles.statNumber} style={{ color: '#dc3545' }}>
            {data.stats.failed_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Public</h3>
          <div className={styles.statNumber} style={{ color: '#17a2b8' }}>
            {data.stats.public_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Total Tokens</h3>
          <div className={styles.statNumber} style={{ color: '#6f42c1' }}>
            {data.stats.total_tokens}
          </div>
        </div>
      </div>
      
      {/* Image Edits Table */}
      <div className={styles.tableContainer}>
        <table className={styles.adminTable}>
          <thead>
            <tr>
              <th>User</th>
              <th>Status</th>
              <th>Prompt</th>
              <th>Images</th>
              <th>Settings</th>
              <th>Tokens</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {data.imageEdits.map((edit) => (
              <tr key={edit.id}>
                <td>
                  <div className={styles.userInfo}>
                    <div>{edit.user_email}</div>
                    <small style={{ color: '#666' }}>{edit.user_role}</small>
                  </div>
                </td>
                <td>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                    <span>{getStatusIcon(edit.status)}</span>
                    <span 
                      className={styles.statusBadge}
                      style={{ backgroundColor: getStatusColor(edit.status) }}
                    >
                      {edit.status}
                    </span>
                  </div>
                  {edit.status === 'failed' && edit.error_message && (
                    <small style={{ color: '#dc3545', display: 'block', marginTop: '0.25rem' }}>
                      {edit.error_message.slice(0, 100)}...
                    </small>
                  )}
                </td>
                <td style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                  {edit.prompt}
                </td>
                <td>
                  <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                    {edit.original_image_url && (
                      <div>
                        <small>Original:</small>
                        <br />
                        <a href={edit.original_image_url} target="_blank" rel="noopener noreferrer">
                          <img 
                            src={edit.original_image_url} 
                            alt="Original" 
                            style={{ maxWidth: 60, maxHeight: 60, borderRadius: 4 }} 
                          />
                        </a>
                      </div>
                    )}
                    {edit.edited_image_url && (
                      <div>
                        <small>Edited:</small>
                        <br />
                        <a href={edit.edited_image_url} target="_blank" rel="noopener noreferrer">
                          <img 
                            src={edit.edited_image_url} 
                            alt="Edited" 
                            style={{ maxWidth: 60, maxHeight: 60, borderRadius: 4 }} 
                          />
                        </a>
                      </div>
                    )}
                    {!edit.edited_image_url && edit.status === 'completed' && (
                      <span style={{ color: '#999' }}>No edited image</span>
                    )}
                  </div>
                </td>
                <td>
                  <small>
                    Guidance: {edit.guidance_scale}<br />
                    Safety: {edit.safety_tolerance}<br />
                    Format: {edit.output_format}
                    {edit.seed && <><br />Seed: {edit.seed}</>}
                  </small>
                </td>
                <td>{edit.token_cost}</td>
                <td>
                  <small>{formatDate(edit.created_at)}</small>
                  {edit.completed_at && (
                    <><br /><small style={{ color: '#28a745' }}>
                      Completed: {formatDate(edit.completed_at)}
                    </small></>
                  )}
                </td>
                <td>
                  <div className={styles.actions}>
                    <button 
                      onClick={() => navigator.clipboard.writeText(edit.id)}
                      className={styles.actionButton}
                      title="Copy ID"
                    >
                      📋
                    </button>
                    {edit.edited_image_url && (
                      <>
                        <a 
                          href={edit.edited_image_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className={styles.actionButton}
                          title="View Image"
                        >
                          👁️
                        </a>
                        <button
                          onClick={() => togglePublicStatus(edit.id, edit.is_public)}
                          className={`${styles.actionButton} ${edit.is_public ? styles.public : styles.private}`}
                          title={edit.is_public ? "Make Private" : "Make Public"}
                        >
                          {edit.is_public ? '🌍' : '🔒'}
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {data.imageEdits.length === 0 && (
        <div className={styles.emptyState}>
          <p>No web image edits found.</p>
        </div>
      )}
    </div>
  );
} 