import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Fetch public image-to-video requests that are completed and have video_url
    const { data: publicVideos, error } = await supabaseAdmin
      .from('image_to_video_requests')
      .select('id, image_url, video_url, prompt, created_at')
      .eq('is_public', true)
      .eq('status', 'completed')
      .not('video_url', 'is', null)
      .order('created_at', { ascending: false })
      .limit(3);

    if (error) {
      console.error('Error fetching public videos:', error);
      return NextResponse.json({ error: 'Failed to fetch public videos' }, { status: 500 });
    }

    return NextResponse.json({ videos: publicVideos || [] });
  } catch (error) {
    console.error('Error in public videos API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
