import React from 'react';
import Button from 'react-bootstrap/Button';
import { format } from 'date-fns';
import styles from './ImageEdit.module.css';

interface ImageEditProps {
  edit: any;
  t: (key: string) => string;
  onViewDetails: (id: string) => void;
  onConvertToVideo?: (imageUrl: string) => void;
}

const ImageEdit: React.FC<ImageEditProps> = ({ edit, t, onViewDetails, onConvertToVideo }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'processing': return '#ffc107';
      case 'pending': return '#ffc107';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Finalizat';
      case 'processing': return 'În Procesare';
      case 'pending': return 'În Procesare';
      case 'failed': return 'Eșuat';
      default: return status;
    }
  };

  return (
  <div className={styles.imageEditCard}>
    <div className={styles.imageEditLeft}>
      <div className={styles.imageEditImages}>
        {edit.original_image_url && (
          <div className={styles.imageEditImageContainer}>
            <img src={edit.original_image_url} alt="Original" className={styles.imageEditImage} />
            <div className={styles.imageEditImageLabel}>{t('image_edit_original')}</div>
          </div>
        )}
        {edit.edited_image_url && (
          <div className={styles.imageEditImageContainer}>
            <img src={edit.edited_image_url} alt="Edited" className={styles.imageEditImage} />
            <div className={styles.imageEditImageLabel}>{t('image_edit_edited')}</div>
          </div>
        )}
      </div>
      <div className={styles.imageEditInfoBlock}>
        <div className={styles.imageEditInfo}>
          <strong>{t('image_edit_prompt')}</strong> <span>{edit.prompt || '-'}</span>
        </div>
        <div className={styles.imageEditInfo}>
          <strong>{t('image_edit_status')}</strong> <span style={{ color: getStatusColor(edit.status), fontWeight: 500 }}>{getStatusText(edit.status)}</span>
        </div>
        <div className={styles.imageEditInfo}>
          <strong>{t('image_edit_cost')}</strong> <span>{edit.token_cost} {t('image_edit_tokens')}</span>
        </div>
        <div className={styles.imageEditInfo}>
          <strong>{t('image_edit_created')}</strong> {edit.created_at ? format(new Date(edit.created_at), 'yyyy-MM-dd HH:mm') : '-'}
        </div>
        {edit.completed_at && (
          <div className={styles.imageEditInfo}>
            <strong>{t('image_edit_completed')}</strong> {format(new Date(edit.completed_at), 'yyyy-MM-dd HH:mm')}
          </div>
        )}
      </div>
    </div>
    <div className={styles.imageEditButtonContainer}>
      <Button variant="primary" className={styles.imageEditButton} onClick={() => onViewDetails(edit.id)}>
        {t('image_edit_view_details')}
      </Button>
      {edit.status === 'completed' && edit.edited_image_url && onConvertToVideo && (
        <Button
          variant="outline-primary"
          className={styles.imageEditButton}
          onClick={() => onConvertToVideo(edit.edited_image_url)}
        >
          {t('image_edit_convert_to_video')}
        </Button>
      )}
    </div>
  </div>
  );
};

export default ImageEdit;
