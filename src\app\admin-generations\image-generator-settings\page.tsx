"use client";

import React, { useEffect, useState } from "react";
import { supabase } from "@/lib/supabaseClient";
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';

const MK_CONFIG_KEY = "mk_image_generator";
const IMAGE_EDITOR_CONFIG_KEY = "image_editor_model";

const IMAGE_EDITOR_OPTIONS = [
  { value: "flux_context_pro", label: "Flux Context PRO" },
  { value: "nano_banana", label: "Nano Banana" },
];

export default function GeneratorSettingsPage() {
  const { user } = useSelector((state: RootState) => state.user);

  // Mortal Kombat Generator Settings
  const [mkOptions, setMkOptions] = useState<string[]>([]);
  const [mkSelected, setMkSelected] = useState<string>("");
  const [mkLoading, setMkLoading] = useState(false);
  const [mkSaving, setMkSaving] = useState(false);
  const [mkMessage, setMkMessage] = useState<string | null>(null);

  // Image Editor Settings
  const [editorSelected, setEditorSelected] = useState<string>(IMAGE_EDITOR_OPTIONS[0].value);
  const [editorLoading, setEditorLoading] = useState(false);
  const [editorSaving, setEditorSaving] = useState(false);
  const [editorMessage, setEditorMessage] = useState<string | null>(null);

  useEffect(() => {
    fetchMkOptionsAndCurrentSetting();
    fetchEditorCurrentSetting();
  }, []);

  const fetchMkOptionsAndCurrentSetting = async () => {
    setMkLoading(true);
    setMkMessage(null);
    const { data, error } = await supabase
      .from("config_options")
      .select("value")
      .eq("key", MK_CONFIG_KEY)
      .single();
    if (error || !data || !data.value || !data.value.options) {
      setMkMessage("Could not load options from Supabase.");
      setMkOptions([]);
      setMkSelected("");
    } else {
      setMkOptions(data.value.options);
      if (data.value.selected && data.value.options.includes(data.value.selected)) {
        setMkSelected(data.value.selected);
      } else {
        setMkSelected(data.value.options[0] || "");
      }
    }
    setMkLoading(false);
  };

  const fetchEditorCurrentSetting = async () => {
    setEditorLoading(true);
    setEditorMessage(null);
    const { data, error } = await supabase
      .from("config_options")
      .select("value")
      .eq("key", IMAGE_EDITOR_CONFIG_KEY)
      .single();
    if (error) {
      setEditorMessage("Could not load current setting. Using default.");
      setEditorSelected(IMAGE_EDITOR_OPTIONS[0].value);
    } else if (data && data.value) {
      setEditorSelected(data.value.selected || IMAGE_EDITOR_OPTIONS[0].value);
    }
    setEditorLoading(false);
  };

  const handleMkSave = async () => {
    setMkSaving(true);
    setMkMessage(null);
    // Save the selected option in the same row, as a 'selected' property
    const { error } = await supabase
      .from("config_options")
      .upsert([
        { key: MK_CONFIG_KEY, value: { options: mkOptions, selected: mkSelected }, updated_at: new Date().toISOString() },
      ]);
    if (error) {
      setMkMessage("Failed to save setting.");
    } else {
      setMkMessage("Setting saved successfully.");
    }
    setMkSaving(false);
  };

  const handleEditorSave = async () => {
    setEditorSaving(true);
    setEditorMessage(null);
    const { error } = await supabase
      .from("config_options")
      .upsert([
        { key: IMAGE_EDITOR_CONFIG_KEY, value: { selected: editorSelected }, updated_at: new Date().toISOString() },
      ]);
    if (error) {
      setEditorMessage("Failed to save setting.");
    } else {
      setEditorMessage("Setting saved successfully.");
    }
    setEditorSaving(false);
  };

  return (
    <div style={{ maxWidth: 800, margin: "2rem auto", padding: 24 }}>
      <h1 style={{ marginBottom: "2rem", textAlign: "center" }}>Generator Settings</h1>

      {/* Mortal Kombat Generator Settings */}
      <div style={{ marginBottom: "3rem", padding: 24, background: "#fff", borderRadius: 8, boxShadow: "0 2px 8px #0001" }}>
        <h2>Mortal Kombat Generator</h2>
        <p>Select which image generator to use for the Mortal Kombat generator.</p>
        <div style={{ margin: "1rem 0" }}>
          <select
            value={mkSelected}
            onChange={e => setMkSelected(e.target.value)}
            disabled={mkLoading || mkSaving || mkOptions.length === 0}
            style={{ padding: 8, fontSize: 16, width: "100%" }}
          >
            {mkOptions.map(opt => (
              <option key={opt} value={opt}>{opt}</option>
            ))}
          </select>
        </div>
        <button
          onClick={handleMkSave}
          disabled={mkSaving || mkLoading || !mkSelected}
          style={{
            padding: "8px 16px",
            fontSize: 16,
            backgroundColor: mkSaving || mkLoading ? "#ccc" : "#007bff",
            color: "white",
            border: "none",
            borderRadius: 4,
            cursor: mkSaving || mkLoading ? "not-allowed" : "pointer"
          }}
        >
          {mkSaving ? "Saving..." : "Save"}
        </button>
        {mkMessage && (
          <div style={{
            marginTop: 16,
            color: mkMessage.includes("success") ? "green" : "red",
            padding: 8,
            backgroundColor: mkMessage.includes("success") ? "#d4edda" : "#f8d7da",
            border: `1px solid ${mkMessage.includes("success") ? "#c3e6cb" : "#f5c6cb"}`,
            borderRadius: 4
          }}>
            {mkMessage}
          </div>
        )}
      </div>

      {/* Image Editor Settings */}
      <div style={{ padding: 24, background: "#fff", borderRadius: 8, boxShadow: "0 2px 8px #0001" }}>
        <h2>Image Editor Settings</h2>
        <p>Select which AI model to use for image editing operations.</p>
        <div style={{ margin: "1rem 0" }}>
          <select
            value={editorSelected}
            onChange={e => setEditorSelected(e.target.value)}
            disabled={editorLoading || editorSaving}
            style={{ padding: 8, fontSize: 16, width: "100%" }}
          >
            {IMAGE_EDITOR_OPTIONS.map(opt => (
              <option key={opt.value} value={opt.value}>{opt.label}</option>
            ))}
          </select>
        </div>
        <div style={{ marginBottom: "1rem", fontSize: 14, color: "#666" }}>
          <strong>Model Information:</strong>
          <ul style={{ marginTop: 8, paddingLeft: 20 }}>
            {editorSelected === "flux_context_pro" && (
              <>
                <li>Flux Context PRO model from FAL.ai</li>
                <li>High-quality image editing with context awareness</li>
                <li>Supports complex prompt-based modifications</li>
              </>
            )}
            {editorSelected === "nano_banana" && (
              <>
                <li>Nano Banana model from FAL.ai</li>
                <li>Fast and efficient image editing</li>
                <li>28 inference steps with 0.8 strength default</li>
                <li>Optimized for quick turnaround times</li>
              </>
            )}
          </ul>
        </div>
        <button
          onClick={handleEditorSave}
          disabled={editorSaving || editorLoading}
          style={{
            padding: "8px 16px",
            fontSize: 16,
            backgroundColor: editorSaving || editorLoading ? "#ccc" : "#007bff",
            color: "white",
            border: "none",
            borderRadius: 4,
            cursor: editorSaving || editorLoading ? "not-allowed" : "pointer"
          }}
        >
          {editorSaving ? "Saving..." : "Save"}
        </button>
        {editorMessage && (
          <div style={{
            marginTop: 16,
            color: editorMessage.includes("success") ? "green" : "red",
            padding: 8,
            backgroundColor: editorMessage.includes("success") ? "#d4edda" : "#f8d7da",
            border: `1px solid ${editorMessage.includes("success") ? "#c3e6cb" : "#f5c6cb"}`,
            borderRadius: 4
          }}>
            {editorMessage}
          </div>
        )}
      </div>
    </div>
  );
}
