import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { processImageEditAsync } from '@/utils/imageEditProcessor';

// Create a Supabase client using the service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const editId = searchParams.get('edit_id');

    if (!editId) {
      return NextResponse.json(
        { error: 'edit_id parameter is required' },
        { status: 400 }
      );
    }

    // Get the complete edit chain
    const { data: editChain, error: chainError } = await supabaseAdmin
      .rpc('get_edit_chain', { edit_id: editId });

    if (chainError) {
      console.error('Error fetching edit chain:', chainError);
      return NextResponse.json(
        { error: 'Failed to fetch edit chain' },
        { status: 500 }
      );
    }

    // Get chain statistics
    const { data: chainStats, error: statsError } = await supabaseAdmin
      .rpc('get_edit_chain_stats', { edit_id: editId });

    if (statsError) {
      console.error('Error fetching chain stats:', statsError);
      return NextResponse.json(
        { error: 'Failed to fetch chain statistics' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      edit_chain: editChain || [],
      statistics: chainStats?.[0] || null
    });

  } catch (error: any) {
    console.error('Edit chain API error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch edit chain', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
}

// POST endpoint to create a chained edit
export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    
    const { 
      parent_edit_id,
      prompt, 
      token_cost = 10,
      guidance_scale = 3.5,
      safety_tolerance = "2",
      output_format = "jpeg",
      seed 
    } = await request.json();

    // Validate required fields
    if (!parent_edit_id || !prompt) {
      return NextResponse.json(
        { error: 'parent_edit_id and prompt are required' },
        { status: 400 }
      );
    }

    // Verify user authentication
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Get the parent edit to validate ownership and get the edited image URL
    const { data: parentEdit, error: parentError } = await supabaseAdmin
      .from('web_image_edits')
      .select('*')
      .eq('id', parent_edit_id)
      .eq('user_id', user.id)
      .single();

    if (parentError || !parentEdit) {
      return NextResponse.json(
        { error: 'Parent edit not found or access denied' },
        { status: 404 }
      );
    }

    if (parentEdit.status !== 'completed' || !parentEdit.edited_image_url) {
      return NextResponse.json(
        { error: 'Parent edit must be completed with a valid edited image' },
        { status: 400 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Check if user has enough tokens
    if (wallet.balance < token_cost) {
      return NextResponse.json(
        { error: 'Insufficient tokens' },
        { status: 400 }
      );
    }

    // Deduct tokens from wallet
    const { error: updateWalletError } = await supabaseAdmin
      .from('wallets')
      .update({ 
        balance: wallet.balance - token_cost,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet.id);

    if (updateWalletError) {
      console.error('Error updating wallet:', updateWalletError);
      return NextResponse.json(
        { error: 'Failed to deduct tokens' },
        { status: 500 }
      );
    }

    // Record the transaction
    const { error: transactionError } = await supabaseAdmin
      .from('token_transactions')
      .insert({
        wallet_id: wallet.id,
        amount: -token_cost,
        description: `Image edit chain - Edit #${parentEdit.edit_sequence + 1}`,
        transaction_type: 'debit'
      });

    if (transactionError) {
      console.error('Error recording transaction:', transactionError);
    }

    // Determine root_edit_id and edit_sequence
    const root_edit_id = parentEdit.root_edit_id || parentEdit.id;
    const edit_sequence = parentEdit.edit_sequence + 1;

    // Create the chained edit record
    const { data: editRecord, error: insertError } = await supabaseAdmin
      .from('web_image_edits')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        parent_edit_id: parent_edit_id,
        root_edit_id: root_edit_id,
        edit_sequence: edit_sequence,
        status: 'pending',
        original_image_url: parentEdit.edited_image_url, // Use parent's edited image as input
        prompt: prompt,
        guidance_scale: guidance_scale,
        safety_tolerance: safety_tolerance,
        output_format: output_format,
        seed: seed,
        token_cost: token_cost,
        is_public: false
      })
      .select()
      .single();

    if (insertError || !editRecord) {
      console.error('Error creating chained edit record:', insertError);
      return NextResponse.json(
        { error: 'Failed to create chained edit record' },
        { status: 500 }
      );
    }

    // Start async processing
    processImageEditAsync(editRecord.id).catch(error => {
      console.error('Async chained processing error:', error);
    });

    return NextResponse.json({
      success: true,
      request_id: editRecord.id,
      parent_edit_id: parent_edit_id,
      root_edit_id: root_edit_id,
      edit_sequence: edit_sequence,
      status: 'pending'
    });

  } catch (error: any) {
    console.error('Chained image editing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to start chained image edit', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
}
