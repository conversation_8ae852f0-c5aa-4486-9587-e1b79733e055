import { createClient } from '@supabase/supabase-js';

// Create Supabase client for storage operations
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
);

/**
 * Upload an image file to Supabase Storage and return the public URL
 * @param file - The image file to upload
 * @param userId - The user ID for organizing files
 * @param folder - Optional folder name (default: 'image-edits')
 * @returns Promise<string> - The public URL of the uploaded image
 */
export async function uploadImageToStorage(
  file: File, 
  userId: string, 
  folder: string = 'image-edits'
): Promise<string> {
  try {
    // Generate unique filename
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const extension = file.name.split('.').pop() || 'jpg';
    const fileName = `${folder}/${userId}/${timestamp}-${randomId}.${extension}`;

    // Upload file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('generated-images')
      .upload(fileName, file, {
        contentType: file.type,
        upsert: false
      });

    if (error) {
      console.error('Storage upload error:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabase.storage
      .from('generated-images')
      .getPublicUrl(fileName);

    if (!urlData?.publicUrl) {
      throw new Error('Failed to get public URL for uploaded image');
    }

    return urlData.publicUrl;
  } catch (error) {
    console.error('Error uploading image to storage:', error);
    throw error;
  }
}

/**
 * Convert a base64 data URL to a File object
 * @param dataUrl - The base64 data URL
 * @param filename - The filename to use
 * @returns File object
 */
export function dataUrlToFile(dataUrl: string, filename: string = 'image.jpg'): File {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new File([u8arr], filename, { type: mime });
}

/**
 * Upload a base64 image to storage (for backward compatibility)
 * @param base64Data - The base64 data URL
 * @param userId - The user ID for organizing files
 * @param folder - Optional folder name
 * @returns Promise<string> - The public URL of the uploaded image
 */
export async function uploadBase64ToStorage(
  base64Data: string,
  userId: string,
  folder: string = 'image-edits'
): Promise<string> {
  const file = dataUrlToFile(base64Data);
  return uploadImageToStorage(file, userId, folder);
}
