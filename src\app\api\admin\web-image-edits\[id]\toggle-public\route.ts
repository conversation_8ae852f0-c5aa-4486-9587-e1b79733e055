import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: editId } = await params;
    const { is_public } = await request.json();

    if (typeof is_public !== 'boolean') {
      return NextResponse.json(
        { error: 'is_public must be a boolean value' },
        { status: 400 }
      );
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify user authentication and admin role
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Check if user is admin (you might need to adjust this based on your user role system)
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('user_id', user.id)
      .single();

    if (profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Update the is_public status
    const { data: updatedEdit, error: updateError } = await supabaseAdmin
      .from('web_image_edits')
      .update({
        is_public: is_public,
        updated_at: new Date().toISOString()
      })
      .eq('id', editId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating public status:', updateError);
      return NextResponse.json(
        { error: 'Failed to update public status' },
        { status: 500 }
      );
    }

    if (!updatedEdit) {
      return NextResponse.json(
        { error: 'Image edit not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Image edit ${is_public ? 'made public' : 'made private'}`,
      edit: updatedEdit
    });

  } catch (error: any) {
    console.error('Error in toggle-public API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 