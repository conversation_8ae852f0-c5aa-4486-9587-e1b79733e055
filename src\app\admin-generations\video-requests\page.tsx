"use client";

import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../../store';
import { supabase } from '../../../lib/supabaseClient';
import VideoPlayer from '@/components/VideoPlayer';
import styles from '../../admin/admin.module.css';

interface VideoRequest {
  id: string;
  user_id: string;
  user_email: string;
  user_role: string;
  web_request_id: string;
  video_url: string;
  duration: number;
  resolution: string;
  prompt_text: string;
  token_cost: number;
  character_type: string;
  style?: string;
  source_image_urls: string[];
  fal_request_id: string;
  fal_model: string;
  created_at: string;
  is_regeneration: boolean;
  request_status: string;
  flow_type: string;
}

interface VideoRequestsData {
  videos: VideoRequest[];
  total_count: number;
  completed_count: number;
  regenerated_count: number;
  total_tokens: number;
}

export default function AdminVideoRequests() {
  const { user } = useSelector((state: RootState) => state.user);
  const [data, setData] = useState<VideoRequestsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    fetchVideoRequests();
  }, []);

  const fetchVideoRequests = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setError('Authentication required');
        return;
      }

      const response = await fetch('/api/admin/video-requests', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${await response.text()}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err: any) {
      console.error('Error fetching video requests:', err);
      setError(err.message || 'Failed to fetch video requests');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getCharacterIcon = (character: string) => {
    const icons: { [key: string]: string } = {
      'scorpion': '🔥',
      'sub-zero': '🧊',
      'raiden': '⚡',
      'liu-kang': '🐉',
      'kitana': '👑'
    };
    return icons[character] || '⚔️';
  };

  if (user?.role !== 'admin') {
    return (
      <div className={styles.errorMessage}>
        <h2>Access Denied</h2>
        <p>Admin access required to view this page.</p>
      </div>
    );
  }

  if (loading) {
    return <div className={styles.loading}>Loading video requests...</div>;
  }

  if (error) {
    return (
      <div className={styles.errorMessage}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={fetchVideoRequests} className={styles.retryButton}>
          Retry
        </button>
      </div>
    );
  }

  if (!data) {
    return <div className={styles.errorMessage}>No data available</div>;
  }

  return (
    <div className={styles.adminPage}>
      <div className={styles.sectionHeader}>
        <h2>Video Requests Management</h2>
        
        <div className={styles.controlsGroup}>
          <div className={styles.togglesContainer}>
            <div className={styles.detailsControl}>
              <label className={styles.detailsToggle}>
                <input
                  type="checkbox"
                  checked={showDetails}
                  onChange={() => setShowDetails(!showDetails)}
                />
                <span className={styles.toggleSwitch}></span>
                <span className={styles.toggleLabel}>
                  Show Details
                </span>
                <span className={styles.toggleStatus}>
                  {showDetails ? 'On' : 'Off'}
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
      
      {/* Statistics Summary */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <h3>Total Videos</h3>
          <div className={styles.statNumber}>{data.total_count}</div>
        </div>
        <div className={styles.statCard}>
          <h3>Completed Requests</h3>
          <div className={styles.statNumber} style={{ color: '#28a745' }}>
            {data.completed_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Regenerated Videos</h3>
          <div className={styles.statNumber} style={{ color: '#ffc107' }}>
            {data.regenerated_count}
          </div>
        </div>
        <div className={styles.statCard}>
          <h3>Total Tokens Used</h3>
          <div className={styles.statNumber} style={{ color: '#6f42c1' }}>
            {data.total_tokens}
          </div>
        </div>
      </div>

      {/* Video Gallery */}
      <div className={showDetails ? styles.videoGallery : styles.videoSimpleGallery}>
        {data.videos.map((video) => (
          showDetails ? (
            // Detailed view
            <div key={video.id} className={styles.videoCard}>
              <div className={styles.videoPreview}>
                <VideoPlayer 
                  src={video.video_url} 
                  className=""
                  style={{ maxHeight: '200px' }}
                />
              </div>
              
              <div className={styles.videoDetails}>
                <div className={styles.videoHeader}>
                  <h4>
                    {getCharacterIcon(video.character_type)} 
                    {video.character_type.charAt(0).toUpperCase() + video.character_type.slice(1).replace('-', ' ')}
                  </h4>
                  {video.is_regeneration && (
                    <span className={styles.regeneratedBadge}>🔄 Regenerated</span>
                  )}
                </div>
                
                <div className={styles.videoMeta}>
                  <div className={styles.metaRow}>
                    <span className={styles.metaLabel}>User:</span>
                    <span className={styles.metaValue}>{video.user_email}</span>
                  </div>
                  <div className={styles.metaRow}>
                    <span className={styles.metaLabel}>Resolution:</span>
                    <span className={styles.metaValue}>{video.resolution}</span>
                  </div>
                  <div className={styles.metaRow}>
                    <span className={styles.metaLabel}>Duration:</span>
                    <span className={styles.metaValue}>{video.duration}s</span>
                  </div>
                  <div className={styles.metaRow}>
                    <span className={styles.metaLabel}>Cost:</span>
                    <span className={styles.metaValue}>{video.token_cost} tokens</span>
                  </div>
                  <div className={styles.metaRow}>
                    <span className={styles.metaLabel}>Created:</span>
                    <span className={styles.metaValue}>{formatDate(video.created_at)}</span>
                  </div>
                </div>

                <div className={styles.videoPrompt}>
                  <span className={styles.metaLabel}>Prompt:</span>
                  <p className={styles.promptText}>{video.prompt_text}</p>
                </div>

                <div className={styles.videoActions}>
                  <button 
                    onClick={() => window.open(`/mortal-kombat-video?id=${video.web_request_id}`, '_blank')}
                    className={styles.actionButton}
                    title="View Request Details"
                  >
                    👁️ View Request
                  </button>
                  <button 
                    onClick={() => navigator.clipboard.writeText(video.id)}
                    className={styles.actionButton}
                    title="Copy Video ID"
                  >
                    📋 Copy ID
                  </button>
                  <a 
                    href={video.video_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className={styles.actionButton}
                    title="Download Video"
                  >
                    💾 Download
                  </a>
                </div>
              </div>
            </div>
          ) : (
            // Simple view - only video
            <div key={video.id} className={styles.videoSimpleCard}>
              <div className={styles.videoPreview}>
                <VideoPlayer 
                  src={video.video_url} 
                  className="simple-video-player"
                  style={{ maxHeight: '300px', borderRadius: '12px' }}
                  hideControlsUntilHover={true}
                />
              </div>
            </div>
          )
        ))}
      </div>

      {data.videos.length === 0 && (
        <div className={styles.emptyState}>
          <p>No video requests found.</p>
        </div>
      )}
    </div>
  );
} 