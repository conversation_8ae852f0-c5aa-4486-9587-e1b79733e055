"use client";

import React, { useState, useRef, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchUserProfile } from '../../store/userSlice';
import { Upload, Image as ImageIcon, Download, ArrowClockwise } from 'react-bootstrap-icons';
import styles from '../admin/admin.module.css';
import Header from '@/components/Header';
import { logger } from '@/utils/logger';
import { supabase } from '../../lib/supabaseClient';
import { useTranslation } from '@/hooks/useTranslation';

interface ImageRestoration {
  request_id: string;
  status: string;
  original_image_url: string;
  restored_image_url?: string;
  output_format: string;
  seed?: number;
  token_cost: number;
  is_public: boolean;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

const ImageRestorationContent: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.user);
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams();
  const requestId = searchParams?.get('id');
  const { t } = useTranslation('ro');

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [currentRestoration, setCurrentRestoration] = useState<ImageRestoration | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [isLoadingExistingRestoration, setIsLoadingExistingRestoration] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // Function to translate status values
  const translateStatus = (status: string): string => {
    switch (status) {
      case 'pending':
        return t('image_restoration_status_pending') || 'Pending';
      case 'processing':
        return t('image_restoration_status_processing') || 'Processing';
      case 'completed':
        return t('image_restoration_status_completed') || 'Completed';
      case 'failed':
        return t('image_restoration_status_failed') || 'Failed';
      default:
        return status;
    }
  };

  // Load existing restoration if ID is provided
  useEffect(() => {
    if (requestId && user) {
      loadExistingRestoration(requestId);
    }
  }, [requestId, user]);

  // Fetch user profile on component mount
  useEffect(() => {
    if (user?.id) {
      dispatch(fetchUserProfile());
    }
  }, [dispatch, user?.id]);

  const loadExistingRestoration = async (id: string) => {
    setIsLoadingExistingRestoration(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const { data, error } = await supabase
        .from('web_image_restorations')
        .select('*')
        .eq('id', id)
        .eq('user_id', user?.id)
        .single();

      if (error) {
        console.error('Error loading restoration:', error);
        setError('Failed to load restoration');
        return;
      }

      if (data) {
        const restoration: ImageRestoration = {
          request_id: data.id,
          status: data.status,
          original_image_url: data.original_image_url,
          restored_image_url: data.restored_image_url,
          output_format: data.output_format,
          seed: data.seed,
          token_cost: data.token_cost,
          is_public: data.is_public,
          error_message: data.error_message,
          created_at: data.created_at,
          updated_at: data.updated_at,
          completed_at: data.completed_at
        };

        setCurrentRestoration(restoration);
        setSelectedImage(restoration.original_image_url);

        // Start polling if restoration is in progress
        if (restoration.status === 'pending' || restoration.status === 'processing') {
          startPolling(id);
        }
      }
    } catch (error: any) {
      console.error('Error loading existing restoration:', error);
      setError(error.message || 'Failed to load restoration');
    } finally {
      setIsLoadingExistingRestoration(false);
    }
  };

  const startPolling = (restorationId: string) => {
    if (isPolling) return;
    
    setIsPolling(true);
    const pollInterval = setInterval(async () => {
      try {
        const { data, error } = await supabase
          .from('web_image_restorations')
          .select('*')
          .eq('id', restorationId)
          .single();

        if (error) {
          console.error('Polling error:', error);
          return;
        }

        if (data) {
          const restoration: ImageRestoration = {
            request_id: data.id,
            status: data.status,
            original_image_url: data.original_image_url,
            restored_image_url: data.restored_image_url,
            output_format: data.output_format,
            seed: data.seed,
            token_cost: data.token_cost,
            is_public: data.is_public,
            error_message: data.error_message,
            created_at: data.created_at,
            updated_at: data.updated_at,
            completed_at: data.completed_at
          };

          setCurrentRestoration(restoration);

          // Stop polling if restoration is complete or failed
          if (restoration.status === 'completed' || restoration.status === 'failed') {
            clearInterval(pollInterval);
            setIsPolling(false);
            setIsGenerating(false);

            if (restoration.status === 'completed') {
              setSuccessMessage('Image restoration completed successfully!');
              // Refresh user profile to update token count
              dispatch(fetchUserProfile());
            } else if (restoration.status === 'failed') {
              setError(restoration.error_message || 'Restoration failed');
            }
          }
        }
      } catch (error) {
        console.error('Polling error:', error);
      }
    }, 3000);

    // Clean up interval after 10 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
      setIsPolling(false);
    }, 600000);
  };

  const handleFileSelect = (file: File) => {
    if (file.type.startsWith('image/')) {
      setSelectedImageFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setSelectedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
      setError(null);
      setCurrentRestoration(null);
    } else {
      setError('Please select a valid image file');
    }
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handlePaste = useCallback((e: ClipboardEvent) => {
    const items = Array.from(e.clipboardData?.items || []);
    const imageItem = items.find(item => item.type.startsWith('image/'));
    
    if (imageItem) {
      const file = imageItem.getAsFile();
      if (file) {
        handleFileSelect(file);
      }
    }
  }, []);

  useEffect(() => {
    const handleGlobalPaste = (e: ClipboardEvent) => {
      if (!currentRestoration) {
        handlePaste(e);
      }
    };

    document.addEventListener('paste', handleGlobalPaste);
    return () => document.removeEventListener('paste', handleGlobalPaste);
  }, [handlePaste, currentRestoration]);

  const uploadImageToStorage = async (file: File): Promise<string> => {
    if (!user) {
      throw new Error('User must be authenticated to upload images');
    }

    try {
      // Generate unique filename
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const extension = file.name.split('.').pop() || 'jpg';
      const fileName = `image-restorations/${user.id}/${timestamp}-${randomId}.${extension}`;

      // Upload file to Supabase Storage
      const { error } = await supabase.storage
        .from('generated-images')
        .upload(fileName, file, {
          contentType: file.type,
          upsert: false
        });

      if (error) {
        console.error('Storage upload error:', error);
        throw new Error(`Failed to upload image: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('generated-images')
        .getPublicUrl(fileName);

      if (!urlData?.publicUrl) {
        throw new Error('Failed to get public URL for uploaded image');
      }

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image to storage:', error);
      throw error;
    }
  };

  const handleRestoreImage = async () => {
    if (!selectedImageFile) {
      setError('Please select an image to restore');
      return;
    }

    if (!user) {
      setError('Please sign in to restore images');
      return;
    }

    // Scroll to top of page to show loading state and results
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setIsGenerating(true);
    setError(null);
    setCurrentRestoration(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      // Upload image to storage to get URL that FAL can access
      const imageDataUrl = await uploadImageToStorage(selectedImageFile);
      
      const response = await fetch('/api/image-restore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          image_url: imageDataUrl,
          output_format: 'jpeg'
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to restore image');
      }

      if (result.success && result.request_id) {
        // Redirect to URL with request ID for persistence
        router.push(`/image-restoration?id=${result.request_id}`);
      } else {
        throw new Error('No request ID returned');
      }

    } catch (error: any) {
      logger.error('Error restoring image:', error);
      setError(error.message || 'Failed to restore image');
      setIsGenerating(false);
    }
  };

  const handleDownload = (imageUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetRestoration = () => {
    setSelectedImage(null);
    setSelectedImageFile(null);
    setCurrentRestoration(null);
    setError(null);
    setSuccessMessage(null);
    setIsPolling(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    // Clear URL params
    router.push('/image-restoration');
  };

  const handleConvertToVideo = (imageUrl: string) => {
    // Navigate to image-to-video page with the restored image URL
    router.push(`/image-to-video?imageUrl=${encodeURIComponent(imageUrl)}`);
  };

  return (
    <>
      <Header />
      <div className={styles.mainContainer}>
        <div className={styles.pageHeader}>
          <h1>Image Restoration</h1>
          <p>Automatically repair defects and colorize your images using AI</p>
        </div>

        <div className={styles.contentContainer}>
          {/* Loading State for Existing Restoration */}
          {isLoadingExistingRestoration && (
            <div className={styles.loadingContainer}>
              <ArrowClockwise size={32} className={styles.spinning} />
              <p>Loading restoration...</p>
            </div>
          )}

          {/* Upload Section - Hide when there are results to show or when loading */}
          {!isLoadingExistingRestoration && !currentRestoration?.restored_image_url && (
          <div className={styles.uploadSection}>
            <div
              ref={dropZoneRef}
              className={`${styles.dropZone} ${selectedImage || currentRestoration?.original_image_url ? styles.hasImage : ''}`}
              onDrop={currentRestoration ? undefined : handleDrop}
              onDragOver={currentRestoration ? undefined : handleDragOver}
              onClick={currentRestoration ? undefined : () => fileInputRef.current?.click()}
            >
              {selectedImage || currentRestoration?.original_image_url ? (
                <div className={styles.selectedImageContainer}>
                  <img
                    src={selectedImage || currentRestoration?.original_image_url}
                    alt="Selected"
                    className={styles.selectedImage}
                  />
                  {!currentRestoration && (
                    <div className={styles.imageOverlay}>
                      <Upload size={24} />
                      <span>Click to replace image</span>
                    </div>
                  )}
                  {(currentRestoration?.status === 'pending' || currentRestoration?.status === 'processing') && (
                    <div className={styles.imageOverlayProcessing}>
                      <ArrowClockwise size={32} className={styles.spinning} />
                    </div>
                  )}
                </div>
              ) : (
                <div className={styles.uploadPrompt}>
                  <ImageIcon size={48} />
                  <h3>Upload Image to Restore</h3>
                  <p>Upload damaged, old, or black & white photos for automatic restoration</p>
                  <p className={styles.supportedFormats}>Supports: JPG, PNG, WebP, GIF</p>
                  <p className={styles.pasteHint}>💡 You can also paste images directly (Ctrl+V)</p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
              style={{ display: 'none' }}
            />
          </div>
          )}

          {/* Restore Controls - Only show when image is selected and no restoration is in progress */}
          {selectedImage && !currentRestoration && (
            <div className={styles.editControls}>
              {/* Insufficient Credits Warning */}
              {user && user.tokens < 10 && (
                <div className={styles.warningMessage}>
                  <strong>⚠️ Insufficient Credits</strong>
                  <p>You need 10 tokens to restore an image. You currently have {user.tokens} tokens.</p>
                </div>
              )}

              <div className={styles.restorationInfo}>
                <h3>🔧 Automatic Image Restoration</h3>
                <p>Our AI will automatically:</p>
                <ul>
                  <li>✨ Repair scratches, tears, and defects</li>
                  <li>🎨 Colorize black & white photos</li>
                  <li>📸 Enhance image quality and clarity</li>
                  <li>🔍 Restore fine details</li>
                </ul>
                <p><strong>Cost:</strong> 10 tokens per restoration</p>
              </div>

              <div className={styles.controlButtons}>
                <button
                  onClick={handleRestoreImage}
                  disabled={isGenerating || !!currentRestoration || (user?.tokens || 0) < 10}
                  className={styles.editButton}
                >
                  {isGenerating || isPolling ? (
                    <>
                      <ArrowClockwise size={16} className={styles.spinning} />
                      {isGenerating ? 'Starting Restoration...' : 'Processing...'}
                    </>
                  ) : (
                    '🔧 Restore Image'
                  )}
                </button>
              </div>
            </div>
          )}

          {/* Error Display */}
          {!isLoadingExistingRestoration && error && (
            <div className={styles.errorMessage}>
              <strong>Error:</strong> {error}
            </div>
          )}

          {/* Success Display */}
          {!isLoadingExistingRestoration && successMessage && (
            <div className={styles.successMessage}>
              <strong>Success:</strong> {successMessage}
            </div>
          )}

          {/* Results Section */}
          {!isLoadingExistingRestoration && ((selectedImage || currentRestoration?.original_image_url) && currentRestoration?.restored_image_url) && (
            <div className={styles.resultsSection}>
              <div className={styles.imageComparison}>
                {currentRestoration?.restored_image_url && (
                  <div className={styles.imagePanel}>
                    <h4>✨ Restored Image</h4>
                    <img src={currentRestoration.restored_image_url} alt="Restored" className={styles.resultImage} />
                  </div>
                )}

                {(selectedImage || currentRestoration?.original_image_url) && (
                  <div className={styles.imagePanel}>
                    <h4>📷 Original Image</h4>
                    <div style={{ position: 'relative', display: 'inline-block', width: 'fit-content' }}>
                      <img
                        src={selectedImage || currentRestoration?.original_image_url}
                        alt="Original"
                        className={styles.resultImage}
                      />
                      {(currentRestoration?.status === 'pending' || currentRestoration?.status === 'processing') && (
                        <div className={styles.imageOverlayProcessing}>
                          <ArrowClockwise size={32} className={styles.spinning} />
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons Section */}
              {currentRestoration?.restored_image_url && (
                <div className={styles.actionsSection}>
                  <button
                    onClick={() => handleDownload(currentRestoration.restored_image_url!, `restored-image-${currentRestoration.request_id}.jpg`)}
                    className={styles.downloadButton}
                  >
                    <Download size={16} />
                    Download Restored Image
                  </button>
                  
                  <button
                    onClick={() => handleConvertToVideo(currentRestoration.restored_image_url!)}
                    className={styles.videoButton}
                  >
                    🎬 Convert to Video
                  </button>

                  <button
                    onClick={resetRestoration}
                    className={styles.resetButton}
                  >
                    🔄 Restore Another Image
                  </button>
                </div>
              )}

              {/* Status Information */}
              {currentRestoration && (
                <div className={styles.statusInfo}>
                  <p><strong>Status:</strong> {translateStatus(currentRestoration.status)}</p>
                  <p><strong>Cost:</strong> {currentRestoration.token_cost} tokens</p>
                  <p><strong>Created:</strong> {new Date(currentRestoration.created_at).toLocaleString()}</p>
                  {currentRestoration.completed_at && (
                    <p><strong>Completed:</strong> {new Date(currentRestoration.completed_at).toLocaleString()}</p>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

const ImageRestorationPage: React.FC = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ImageRestorationContent />
    </Suspense>
  );
};

export default ImageRestorationPage;
