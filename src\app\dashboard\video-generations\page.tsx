"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';
import VideoGeneration from '../VideoGeneration';
import { useTranslation } from '@/hooks/useTranslation';
import Header from '@/components/Header';
import styles from './page.module.css';

type FilterStatus = 'all' | 'completed' | 'processing';

export default function VideoGenerationsPage() {
  const router = useRouter();
  const [videoGenerations, setVideoGenerations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState<FilterStatus>('all');
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  useEffect(() => {
    async function fetchVideoGenerations() {
      setLoading(true);
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.user?.id) {
        router.push('/login');
        return;
      }

      const { data, error } = await supabase
        .from('image_to_video_requests')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });
      
      if (!error && data) {
        console.log('Video generations data:', data);
        setVideoGenerations(data);
      } else {
        console.error('Error fetching video generations:', error);
      }
      setLoading(false);
    }
    fetchVideoGenerations();
  }, [router]);

  // Filter videos based on selected status
  const filteredVideos = videoGenerations.filter(video => {
    if (statusFilter === 'all') return true;
    return video.status === statusFilter;
  });

  const getFilterCount = (status: FilterStatus) => {
    if (status === 'all') return videoGenerations.length;
    return videoGenerations.filter(video => video.status === status).length;
  };

  return (
    <div className={styles.videoGenerationsPage}>
      <Header />
      <div className="container mt-4">
        <div className="row">
          <div className="col-12">
            <h2 className="mb-4">{t('video_generations_title')}</h2>
            
            {/* Status Filter Tabs */}
            <div className={styles.statusFilterTabs}>
              <button 
                className={`${styles.filterTab} ${statusFilter === 'all' ? `${styles.active} ${styles.allTab}` : ''}`}
                onClick={() => setStatusFilter('all')}
              >
                {t('video_generations_filter_all')} ({videoGenerations.length})
              </button>
              <button 
                className={`${styles.filterTab} ${statusFilter === 'completed' ? `${styles.active} ${styles.completedTab}` : ''}`}
                onClick={() => setStatusFilter('completed')}
              >
                {t('video_generations_filter_completed')} ({filteredVideos.filter(v => v.status === 'completed').length})
              </button>
              <button 
                className={`${styles.filterTab} ${statusFilter === 'processing' ? `${styles.active} ${styles.processingTab}` : ''}`}
                onClick={() => setStatusFilter('processing')}
              >
                {t('video_generations_filter_processing')} ({filteredVideos.filter(v => v.status === 'processing').length})
              </button>
            </div>

        {loading ? (
          <div style={{ color: '#888', fontSize: 18 }}>Se încarcă...</div>
        ) : filteredVideos.length === 0 ? (
          <div style={{ color: '#888', fontSize: 18 }}>
            {statusFilter === 'all' 
              ? t('video_generations_no_videos')
              : `No ${statusFilter} videos found`
            }
          </div>
        ) : (
          <div style={{ display: 'flex', flexDirection: 'column', gap: 20 }}>
            {filteredVideos.map(video => (
              <VideoGeneration
                key={video.id}
                video={video}
                t={t}
                onViewVideo={(video) => {
                  setSelectedVideo(video);
                  setShowVideoModal(true);
                }}
              />
            ))}
          </div>
        )}
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {showVideoModal && selectedVideo && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}
          onClick={() => setShowVideoModal(false)}
        >
          <div 
            style={{
              backgroundColor: 'white',
              borderRadius: 8,
              padding: 24,
              maxWidth: '90vw',
              maxHeight: '90vh',
              overflow: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
              <h3 style={{ margin: 0 }}>Video Generation</h3>
              <button 
                onClick={() => setShowVideoModal(false)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: 24,
                  cursor: 'pointer',
                  padding: 0,
                  width: 32,
                  height: 32,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                ×
              </button>
            </div>
            
            {selectedVideo.video_url ? (
              <video 
                controls 
                style={{ width: '100%', maxWidth: 600, height: 'auto' }}
                src={selectedVideo.video_url}
              />
            ) : (
              <div style={{ color: '#888', textAlign: 'center', padding: 40 }}>
                Video not available
              </div>
            )}
            
            <div style={{ marginTop: 16 }}>
              <p><strong>Prompt:</strong> {selectedVideo.prompt || 'N/A'}</p>
              <p><strong>Status:</strong> {selectedVideo.status}</p>
              <p><strong>Duration:</strong> {selectedVideo.duration}s</p>
              <p><strong>Resolution:</strong> {selectedVideo.resolution}</p>
              <p><strong>Created:</strong> {selectedVideo.created_at ? new Date(selectedVideo.created_at).toLocaleString() : 'N/A'}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
