

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE OR REPLACE FUNCTION "public"."add_test_user"("email" "text", "password" "text", "is_confirmed" boolean DEFAULT true) RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $_$
DECLARE
  v_user_id UUID;
  v_encrypted_password TEXT;
BEGIN
  -- Generate a UUID for the user
  v_user_id := uuid_generate_v4();
  
  -- Simulate the encrypted password hash
  v_encrypted_password := '$2a$10$abcdefghijklmnopqrstuvwxyz123456789';
  
  -- Insert the user directly
  INSERT INTO auth.users (
    id,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    is_anonymous,
    is_sso_user
  )
  VALUES (
    v_user_id,
    email,
    v_encrypted_password,
    CASE WHEN is_confirmed THEN now() ELSE NULL END,
    now(),
    now(),
    false,
    false
  );
  
  RETURN v_user_id;
END;
$_$;


ALTER FUNCTION "public"."add_test_user"("email" "text", "password" "text", "is_confirmed" boolean) OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."complete_payment"("payment_id_param" "uuid", "stripe_payment_intent_id_param" "text") RETURNS "json"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    payment_record payments;
    package_record token_packages;
    user_wallet_id uuid;
    tokens_to_grant integer;
    result json;
BEGIN
    -- Get payment record (look for completed payments that haven't granted tokens yet)
    SELECT * INTO payment_record 
    FROM payments 
    WHERE id = payment_id_param 
    AND stripe_payment_intent_id = stripe_payment_intent_id_param
    AND status = 'completed'
    AND tokens_granted IS NULL;  -- This is the key fix!
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Payment not found or already processed'
        );
    END IF;
    
    -- Get token package details
    SELECT * INTO package_record 
    FROM token_packages 
    WHERE id = payment_record.token_package_id;
    
    -- Find or create user wallet
    SELECT id INTO user_wallet_id 
    FROM wallets 
    WHERE user_id = payment_record.user_id;
    
    IF NOT FOUND THEN
        INSERT INTO wallets (user_id, balance) 
        VALUES (payment_record.user_id, 0) 
        RETURNING id INTO user_wallet_id;
    END IF;
    
    -- Update wallet balance
    UPDATE wallets 
    SET balance = balance + package_record.token_amount,
        updated_at = now()
    WHERE id = user_wallet_id;
    
    -- Update payment with granted tokens
    UPDATE payments 
    SET tokens_granted = package_record.token_amount,
        updated_at = now()
    WHERE id = payment_id_param;
    
    -- Create transaction record
    INSERT INTO token_transactions (
        wallet_id, 
        amount, 
        description, 
        transaction_type
    ) VALUES (
        user_wallet_id,
        package_record.token_amount,
        'Token purchase: ' || package_record.name,
        'purchase'
    );
    
    RETURN json_build_object(
        'success', true,
        'tokens_granted', package_record.token_amount,
        'new_balance', (SELECT balance FROM wallets WHERE id = user_wallet_id)
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$;


ALTER FUNCTION "public"."complete_payment"("payment_id_param" "uuid", "stripe_payment_intent_id_param" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."confirm_user"("user_email" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public', 'auth'
    AS $$
DECLARE
  v_count INTEGER;
BEGIN
  UPDATE auth.users 
  SET email_confirmed_at = now()
  WHERE email = user_email;
  
  GET DIAGNOSTICS v_count = ROW_COUNT;
  
  RETURN v_count > 0;
END;
$$;


ALTER FUNCTION "public"."confirm_user"("user_email" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."consume_tokens"("p_wallet_id" "uuid", "p_amount" integer, "p_description" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_current_balance INTEGER;
BEGIN
    -- Validate amount
    IF p_amount <= 0 THEN
        RAISE EXCEPTION 'Token amount must be positive';
    END IF;
    
    -- Lock the wallet row for update
    SELECT balance INTO v_current_balance
    FROM public.wallets
    WHERE id = p_wallet_id
    FOR UPDATE;
    
    -- Check if wallet exists
    IF v_current_balance IS NULL THEN
        RAISE EXCEPTION 'Wallet not found';
    END IF;
    
    -- Check if sufficient balance
    IF v_current_balance < p_amount THEN
        RETURN FALSE;
    END IF;
    
    -- Update wallet balance
    UPDATE public.wallets
    SET balance = balance - p_amount,
        updated_at = now()
    WHERE id = p_wallet_id;
    
    -- Record the transaction
    INSERT INTO public.token_transactions (
        wallet_id,
        amount,
        description,
        transaction_type
    )
    VALUES (
        p_wallet_id,
        -p_amount,
        p_description,
        'consumption'
    );
    
    RETURN TRUE;
END;
$$;


ALTER FUNCTION "public"."consume_tokens"("p_wallet_id" "uuid", "p_amount" integer, "p_description" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."create_user_profile"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public', 'auth'
    AS $$
DECLARE
    v_wallet_id UUID;
    v_initial_tokens INTEGER := 100; -- Default initial token balance
    v_profile_exists BOOLEAN;
BEGIN
    -- Check if profile already exists
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE user_id = NEW.id) INTO v_profile_exists;
    
    -- Only insert if profile doesn't exist
    IF NOT v_profile_exists THEN
        -- Insert a profile for the new user
        INSERT INTO public.profiles (user_id, tokens, role)
        VALUES (NEW.id, v_initial_tokens, 'user');
    END IF;
    
    -- Check if wallet already exists
    IF NOT EXISTS (SELECT 1 FROM public.wallets WHERE user_id = NEW.id) THEN
        -- Create a wallet for the new user
        INSERT INTO public.wallets (user_id, balance, updated_at)
        VALUES (NEW.id, v_initial_tokens, now())
        RETURNING id INTO v_wallet_id;
        
        -- Record the token transaction
        INSERT INTO public.token_transactions (
            wallet_id,
            amount,
            description,
            transaction_type
        )
        VALUES (
            v_wallet_id,
            v_initial_tokens,
            'Initial web account allocation',
            'airdrop'
        );
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."create_user_profile"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."deduct_tokens_for_new_chat"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE 
  current_tokens INTEGER;
BEGIN
  -- Look up the current user's token balance.
  SELECT tokens INTO current_tokens FROM profiles WHERE user_id = NEW.user_id;
  
  IF current_tokens IS NULL THEN
    RAISE EXCEPTION 'User profile not found for user_id: %', NEW.user_id;
  ELSIF current_tokens < 10 THEN
    RAISE EXCEPTION 'Insufficient tokens. Current balance: %', current_tokens;
  END IF;
  
  -- Deduct 10 tokens from the user's profile.
  UPDATE profiles 
  SET tokens = tokens - 10 
  WHERE user_id = NEW.user_id;
  
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."deduct_tokens_for_new_chat"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."find_external_identity_by_id"("p_external_identity_id" "text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_identity_id UUID;
BEGIN
  -- Try to find the external identity
  SELECT id INTO v_identity_id
  FROM external_identities
  WHERE external_identity_id = p_external_identity_id;
  
  RETURN v_identity_id;
END;
$$;


ALTER FUNCTION "public"."find_external_identity_by_id"("p_external_identity_id" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_edited_image"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_original_upload_id" "uuid", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_image_id UUID;
    v_token_consumption_success BOOLEAN;
BEGIN
    -- Consume tokens first
    v_token_consumption_success := public.consume_tokens(
        p_wallet_id := p_wallet_id,
        p_amount := p_token_cost,
        p_description := 'Image editing'
    );
    
    -- If token consumption fails, return error
    IF NOT v_token_consumption_success THEN
        RAISE EXCEPTION 'Insufficient tokens for image editing';
    END IF;
    
    -- Record the edited image
    INSERT INTO public.images (
        wallet_id,
        conversation_id,
        source_type,
        original_upload_id,
        parameters,
        result_url
    )
    VALUES (
        p_wallet_id,
        p_conversation_id,
        'edited',
        p_original_upload_id,
        p_parameters,
        p_result_url
    )
    RETURNING id INTO v_image_id;
    
    RETURN v_image_id;
END;
$$;


ALTER FUNCTION "public"."generate_edited_image"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_original_upload_id" "uuid", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."generate_image_from_text"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_prompt_text" "text", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_image_id UUID;
    v_token_consumption_success BOOLEAN;
BEGIN
    -- Consume tokens first
    v_token_consumption_success := public.consume_tokens(
        p_wallet_id := p_wallet_id,
        p_amount := p_token_cost,
        p_description := 'Image generation: ' || LEFT(p_prompt_text, 50) || CASE WHEN LENGTH(p_prompt_text) > 50 THEN '...' ELSE '' END
    );
    
    -- If token consumption fails, return error
    IF NOT v_token_consumption_success THEN
        RAISE EXCEPTION 'Insufficient tokens for image generation';
    END IF;
    
    -- Record the generated image
    INSERT INTO public.images (
        wallet_id,
        conversation_id,
        source_type,
        prompt_text,
        parameters,
        result_url
    )
    VALUES (
        p_wallet_id,
        p_conversation_id,
        'generated',
        p_prompt_text,
        p_parameters,
        p_result_url
    )
    RETURNING id INTO v_image_id;
    
    RETURN v_image_id;
END;
$$;


ALTER FUNCTION "public"."generate_image_from_text"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_prompt_text" "text", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_admin_users_with_activity"() RETURNS TABLE("id" "text", "email" "text", "telegram_id" "text", "telegram_username" "text", "whatsapp_id" "text", "whatsapp_name" "text", "tokens" bigint, "message_count" bigint, "last_active" timestamp with time zone, "created_at" timestamp with time zone, "is_admin" boolean, "is_bot_user" boolean, "raw_user_meta_data" "jsonb")
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
  RETURN QUERY
  WITH user_activities AS (
    -- Get bot users from external_identities
    SELECT DISTINCT
      ei.id::text as user_id,
      null::text as email,
      ei.telegram_id::text,
      COALESCE(ei.telegram_first_name || ' ' || ei.telegram_last_name, ei.telegram_first_name, ei.telegram_last_name)::text as telegram_username,
      ei.whatsapp_id::text,
      ei.whatsapp_name::text,
      COALESCE(w.balance, 0) as balance,
      ei.created_at,
      false as is_admin,
      true as is_bot_user,
      '{}'::jsonb as raw_user_meta_data
    FROM external_identities ei
    LEFT JOIN wallets w ON w.external_identity_id = ei.id
    WHERE ei.id IS NOT NULL
    
    UNION ALL
    
    -- Get web users from auth.users
    SELECT DISTINCT
      au.id::text as user_id,
      au.email,
      null::text as telegram_id,
      null::text as telegram_username,
      null::text as whatsapp_id,
      null::text as whatsapp_name,
      COALESCE(w.balance, 0) as balance,
      au.created_at,
      COALESCE(p.role = 'admin', false) as is_admin,
      false as is_bot_user,
      COALESCE(au.raw_user_meta_data, '{}'::jsonb) as raw_user_meta_data
    FROM auth.users au
    LEFT JOIN wallets w ON w.user_id = au.id
    LEFT JOIN profiles p ON p.user_id = au.id
    WHERE au.id IS NOT NULL
  ),
  activity_timestamps AS (
    -- Bot messages activity (from messages table)
    SELECT 
      bc.external_identity_id::text as activity_user_id,
      MAX(m.created_at) as last_activity_time
    FROM messages m
    JOIN bot_conversations bc ON m.conversation_id = bc.id
    WHERE m.sender = 'user'
    GROUP BY bc.external_identity_id::text
    
    UNION ALL
    
    -- Web generation requests activity
    SELECT 
      wgr.user_id::text as activity_user_id,
      MAX(wgr.updated_at) as last_activity_time
    FROM web_generation_requests wgr
    WHERE wgr.user_id IS NOT NULL
    GROUP BY wgr.user_id::text
    
    UNION ALL
    
    -- Web images activity
    SELECT 
      wi.user_id::text as activity_user_id,
      MAX(wi.created_at) as last_activity_time
    FROM web_images wi
    WHERE wi.user_id IS NOT NULL
    GROUP BY wi.user_id::text
    
    UNION ALL
    
    -- Web videos activity
    SELECT 
      wv.user_id::text as activity_user_id,
      MAX(wv.created_at) as last_activity_time
    FROM web_videos wv
    WHERE wv.user_id IS NOT NULL
    GROUP BY wv.user_id::text
  ),
  latest_activity AS (
    SELECT 
      activity_user_id,
      MAX(last_activity_time) as last_active_time
    FROM activity_timestamps
    GROUP BY activity_user_id
  ),
  message_counts AS (
    -- Count messages from bot users
    SELECT 
      bc.external_identity_id::text as msg_user_id,
      COUNT(*) as total_messages
    FROM messages m
    JOIN bot_conversations bc ON m.conversation_id = bc.id
    WHERE m.sender = 'user'
    GROUP BY bc.external_identity_id::text
  )
  SELECT 
    ua.user_id,
    ua.email,
    ua.telegram_id,
    ua.telegram_username,
    ua.whatsapp_id,
    ua.whatsapp_name,
    ua.balance::BIGINT,
    COALESCE(mc.total_messages, 0)::BIGINT as message_count,
    la.last_active_time,
    ua.created_at,
    ua.is_admin,
    ua.is_bot_user,
    ua.raw_user_meta_data
  FROM user_activities ua
  LEFT JOIN latest_activity la ON ua.user_id = la.activity_user_id
  LEFT JOIN message_counts mc ON ua.user_id = mc.msg_user_id
  ORDER BY la.last_active_time DESC NULLS LAST, ua.user_id;
END;
$$;


ALTER FUNCTION "public"."get_admin_users_with_activity"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_bot_activity"() RETURNS TABLE("provider" "text", "provider_bot_id" "text", "name" "text", "conversation_count" bigint, "message_count" bigint)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.provider,
        b.provider_bot_id,
        b.name,
        COUNT(DISTINCT c.id) AS conversation_count,
        COUNT(DISTINCT m.id) AS message_count
    FROM
        bots b
    LEFT JOIN
        bot_conversations c ON b.id = c.bot_id
    LEFT JOIN
        messages m ON c.id = m.conversation_id
    GROUP BY
        b.id, b.provider, b.provider_bot_id, b.name
    ORDER BY
        conversation_count DESC;
END; $$;


ALTER FUNCTION "public"."get_bot_activity"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_or_create_bot_conversation"("p_external_identity_id" "uuid", "p_bot_id" "uuid", "p_provider" "text", "p_provider_conversation_id" "text" DEFAULT NULL::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_conversation_id UUID;
BEGIN
    -- Try to find existing conversation by provider conversation ID
    IF p_provider_conversation_id IS NOT NULL THEN
        SELECT id INTO v_conversation_id
        FROM public.bot_conversations
        WHERE provider = p_provider
        AND provider_conversation_id = p_provider_conversation_id;
        
        -- If found, return it
        IF v_conversation_id IS NOT NULL THEN
            RETURN v_conversation_id;
        END IF;
    END IF;
    
    -- Create new conversation
    INSERT INTO public.bot_conversations (
        external_identity_id,
        bot_id,
        provider,
        provider_conversation_id
    )
    VALUES (
        p_external_identity_id,
        p_bot_id,
        p_provider,
        p_provider_conversation_id
    )
    RETURNING id INTO v_conversation_id;
    
    RETURN v_conversation_id;
END;
$$;


ALTER FUNCTION "public"."get_or_create_bot_conversation"("p_external_identity_id" "uuid", "p_bot_id" "uuid", "p_provider" "text", "p_provider_conversation_id" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."get_or_create_external_wallet"("p_telegram_id" "text" DEFAULT NULL::"text", "p_whatsapp_id" "text" DEFAULT NULL::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_external_identity_id UUID;
    v_wallet_id UUID;
    v_provider TEXT;
    v_airdrop_granted BOOLEAN;
    v_airdrop_amount INTEGER := 100; -- Default airdrop amount
BEGIN
    -- Validate input: at least one ID must be provided
    IF p_telegram_id IS NULL AND p_whatsapp_id IS NULL THEN
        RAISE EXCEPTION 'At least one external ID (Telegram or WhatsApp) must be provided';
    END IF;
    
    -- Determine which provider we're handling
    IF p_telegram_id IS NOT NULL THEN
        v_provider := 'telegram';
    ELSE
        v_provider := 'whatsapp';
    END IF;
    
    -- Get or create the external identity
    BEGIN
        IF p_telegram_id IS NOT NULL THEN
            -- Try to get existing identity by Telegram ID
            SELECT id INTO v_external_identity_id
            FROM public.external_identities
            WHERE telegram_id = p_telegram_id;
            
            -- Check if airdrop was already granted
            IF v_external_identity_id IS NOT NULL THEN
                SELECT telegram_airdrop_granted INTO v_airdrop_granted
                FROM public.external_identities
                WHERE id = v_external_identity_id;
            END IF;
            
            -- Create new identity if not found
            IF v_external_identity_id IS NULL THEN
                INSERT INTO public.external_identities (telegram_id)
                VALUES (p_telegram_id)
                RETURNING id INTO v_external_identity_id;
                
                v_airdrop_granted := FALSE;
            END IF;
        ELSE
            -- Try to get existing identity by WhatsApp ID
            SELECT id INTO v_external_identity_id
            FROM public.external_identities
            WHERE whatsapp_id = p_whatsapp_id;
            
            -- Check if airdrop was already granted
            IF v_external_identity_id IS NOT NULL THEN
                SELECT whatsapp_airdrop_granted INTO v_airdrop_granted
                FROM public.external_identities
                WHERE id = v_external_identity_id;
            END IF;
            
            -- Create new identity if not found
            IF v_external_identity_id IS NULL THEN
                INSERT INTO public.external_identities (whatsapp_id)
                VALUES (p_whatsapp_id)
                RETURNING id INTO v_external_identity_id;
                
                v_airdrop_granted := FALSE;
            END IF;
        END IF;
    EXCEPTION
        WHEN unique_violation THEN
            -- Handle race condition
            IF p_telegram_id IS NOT NULL THEN
                SELECT id, telegram_airdrop_granted INTO v_external_identity_id, v_airdrop_granted
                FROM public.external_identities
                WHERE telegram_id = p_telegram_id;
            ELSE
                SELECT id, whatsapp_airdrop_granted INTO v_external_identity_id, v_airdrop_granted
                FROM public.external_identities
                WHERE whatsapp_id = p_whatsapp_id;
            END IF;
    END;
    
    -- Get or create the wallet
    BEGIN
        -- Try to get existing wallet
        SELECT id INTO v_wallet_id
        FROM public.wallets
        WHERE external_identity_id = v_external_identity_id;
        
        -- Create new wallet if not found
        IF v_wallet_id IS NULL THEN
            INSERT INTO public.wallets (external_identity_id, balance)
            VALUES (v_external_identity_id, 0)
            RETURNING id INTO v_wallet_id;
        END IF;
    EXCEPTION
        WHEN unique_violation THEN
            -- Handle race condition
            SELECT id INTO v_wallet_id
            FROM public.wallets
            WHERE external_identity_id = v_external_identity_id;
    END;
    
    -- Grant airdrop if not already granted
    IF NOT v_airdrop_granted THEN
        -- Update wallet balance
        UPDATE public.wallets
        SET balance = balance + v_airdrop_amount,
            updated_at = now()
        WHERE id = v_wallet_id;
        
        -- Record the transaction
        INSERT INTO public.token_transactions (
            wallet_id,
            amount,
            description,
            transaction_type
        )
        VALUES (
            v_wallet_id,
            v_airdrop_amount,
            'Initial ' || v_provider || ' airdrop',
            'airdrop'
        );
        
        -- Mark airdrop as granted
        IF v_provider = 'telegram' THEN
            UPDATE public.external_identities
            SET telegram_airdrop_granted = TRUE
            WHERE id = v_external_identity_id;
        ELSE
            UPDATE public.external_identities
            SET whatsapp_airdrop_granted = TRUE
            WHERE id = v_external_identity_id;
        END IF;
    END IF;
    
    RETURN v_wallet_id;
END;
$$;


ALTER FUNCTION "public"."get_or_create_external_wallet"("p_telegram_id" "text", "p_whatsapp_id" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."handle_auth_user_created"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public', 'auth'
    AS $$
DECLARE
    v_wallet_id UUID;
    v_initial_tokens INTEGER := 100; -- Default initial token balance
    v_profile_exists BOOLEAN;
BEGIN
    -- Check if profile already exists
    SELECT EXISTS(SELECT 1 FROM public.profiles WHERE user_id = NEW.id) INTO v_profile_exists;
    
    -- Only insert if profile doesn't exist
    IF NOT v_profile_exists THEN
        -- Insert a profile for the new user
        INSERT INTO public.profiles (user_id, tokens, role)
        VALUES (NEW.id, v_initial_tokens, 'user');
    END IF;
    
    -- Check if wallet already exists
    IF NOT EXISTS (SELECT 1 FROM public.wallets WHERE user_id = NEW.id) THEN
        -- Create a wallet for the new user
        INSERT INTO public.wallets (user_id, balance, updated_at)
        VALUES (NEW.id, v_initial_tokens, now())
        RETURNING id INTO v_wallet_id;
        
        -- Record the token transaction
        INSERT INTO public.token_transactions (
            wallet_id,
            amount,
            description,
            transaction_type
        )
        VALUES (
            v_wallet_id,
            v_initial_tokens,
            'Initial web account allocation',
            'airdrop'
        );
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_auth_user_created"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."insert_message_from_api"("p_conversation_id" "text", "p_content" "text", "p_upload_id" "text", "p_sender" "text" DEFAULT 'user'::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_message_id UUID;
  v_conversation_id UUID;
  v_upload_id UUID;
  v_conversation_record bot_conversations%ROWTYPE;
BEGIN
  -- Convert text IDs to UUIDs
  BEGIN
    v_conversation_id := p_conversation_id::UUID;
  EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid conversation_id format: %', p_conversation_id;
  END;
  
  IF p_upload_id IS NOT NULL THEN
    BEGIN
      v_upload_id := p_upload_id::UUID;
    EXCEPTION WHEN others THEN
      RAISE EXCEPTION 'Invalid upload_id format: %', p_upload_id;
    END;
  END IF;
  
  -- Get the conversation record
  SELECT * INTO v_conversation_record 
  FROM bot_conversations 
  WHERE id = v_conversation_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Conversation with ID % not found', v_conversation_id;
  END IF;
  
  -- Insert the message record with only upload_id (to respect the constraint)
  INSERT INTO messages (
    conversation_id,
    sender,
    content,
    upload_id
  ) VALUES (
    v_conversation_id,
    p_sender,
    p_content,
    v_upload_id
  )
  RETURNING id INTO v_message_id;
  
  -- Trigger real-time notifications
  PERFORM pg_notify(
    'conversation_updates',
    json_build_object(
      'conversation_id', v_conversation_id,
      'provider', v_conversation_record.provider,
      'event', 'new_message',
      'message_id', v_message_id
    )::text
  );
  
  RETURN v_message_id;
END;
$$;


ALTER FUNCTION "public"."insert_message_from_api"("p_conversation_id" "text", "p_content" "text", "p_upload_id" "text", "p_sender" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."insert_user_profile"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public', 'auth'
    AS $$
DECLARE
    v_wallet_id UUID;
    v_initial_tokens INTEGER := 100; -- Default initial token balance
BEGIN
    -- We'll let create_user_profile handle the profile creation
    -- Just focus on wallet creation if needed
    
    -- Check if wallet already exists
    IF NOT EXISTS (SELECT 1 FROM public.wallets WHERE user_id = NEW.id) THEN
        -- Create a wallet for the new user
        INSERT INTO public.wallets (user_id, balance, updated_at)
        VALUES (NEW.id, v_initial_tokens, now())
        RETURNING id INTO v_wallet_id;
        
        -- Record the token transaction
        INSERT INTO public.token_transactions (
            wallet_id,
            amount,
            description,
            transaction_type
        )
        VALUES (
            v_wallet_id,
            v_initial_tokens,
            'Initial web account allocation',
            'airdrop'
        );
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."insert_user_profile"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."insert_user_upload_from_api"("p_conversation_id" "text", "p_external_identity_id_text" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text" DEFAULT 'user'::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_upload_id UUID;
  v_conversation_id UUID;
  v_external_identity_id UUID;
BEGIN
  -- Convert text IDs to UUIDs
  BEGIN
    v_conversation_id := p_conversation_id::UUID;
  EXCEPTION WHEN others THEN
    RAISE EXCEPTION 'Invalid conversation_id format: %', p_conversation_id;
  END;
  
  BEGIN
    v_external_identity_id := p_external_identity_id_text::UUID;
  EXCEPTION WHEN others THEN
    -- If direct conversion fails, try to look up by string ID
    SELECT id INTO v_external_identity_id
    FROM external_identities
    WHERE external_identity_id = p_external_identity_id_text;
    
    IF v_external_identity_id IS NULL THEN
      RAISE EXCEPTION 'Could not find external identity with ID: %', p_external_identity_id_text;
    END IF;
  END;
  
  -- Insert the upload record
  INSERT INTO user_uploads (
    conversation_id,
    external_identity_id,
    file_url,
    metadata,
    sender
  ) VALUES (
    v_conversation_id,
    v_external_identity_id,
    p_file_url,
    p_metadata,
    p_sender
  )
  RETURNING id INTO v_upload_id;
  
  RETURN v_upload_id;
END;
$$;


ALTER FUNCTION "public"."insert_user_upload_from_api"("p_conversation_id" "text", "p_external_identity_id_text" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."link_external_identity_to_user"("p_user_id" "uuid", "p_external_identity_id" "uuid") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_external_wallet_id UUID;
    v_user_wallet_id UUID;
    v_external_balance INTEGER;
    v_user_balance INTEGER;
BEGIN
    -- Check if the external identity exists
    IF NOT EXISTS (SELECT 1 FROM public.external_identities WHERE id = p_external_identity_id) THEN
        RAISE EXCEPTION 'External identity not found';
    END IF;
    
    -- Check if the user exists
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = p_user_id) THEN
        RAISE EXCEPTION 'User not found';
    END IF;
    
    -- Check if the external identity is already linked to a user
    IF EXISTS (SELECT 1 FROM public.external_identities WHERE id = p_external_identity_id AND user_id IS NOT NULL) THEN
        RAISE EXCEPTION 'External identity is already linked to a user';
    END IF;
    
    -- Start transaction
    BEGIN
        -- Link the external identity to the user
        UPDATE public.external_identities
        SET user_id = p_user_id
        WHERE id = p_external_identity_id;
        
        -- Find the external wallet
        SELECT id, balance INTO v_external_wallet_id, v_external_balance
        FROM public.wallets
        WHERE external_identity_id = p_external_identity_id;
        
        -- Find or create the user's wallet
        BEGIN
            -- Try to get existing user wallet
            SELECT id, balance INTO v_user_wallet_id, v_user_balance
            FROM public.wallets
            WHERE user_id = p_user_id;
            
            -- Create new wallet if not found
            IF v_user_wallet_id IS NULL THEN
                INSERT INTO public.wallets (user_id, balance)
                VALUES (p_user_id, 0)
                RETURNING id INTO v_user_wallet_id;
                
                v_user_balance := 0;
            END IF;
        END;
        
        -- If both wallets exist, transfer balance from external to user wallet
        IF v_external_wallet_id IS NOT NULL AND v_user_wallet_id IS NOT NULL AND v_external_balance > 0 THEN
            -- Update user wallet
            UPDATE public.wallets
            SET balance = balance + v_external_balance,
                updated_at = now()
            WHERE id = v_user_wallet_id;
            
            -- Record the transfer transaction for user wallet
            INSERT INTO public.token_transactions (
                wallet_id,
                amount,
                description,
                transaction_type
            )
            VALUES (
                v_user_wallet_id,
                v_external_balance,
                'Transfer from linked external account',
                'transfer'
            );
            
            -- Zero out the external wallet
            UPDATE public.wallets
            SET balance = 0,
                updated_at = now()
            WHERE id = v_external_wallet_id;
            
            -- Record the transfer transaction for external wallet
            INSERT INTO public.token_transactions (
                wallet_id,
                amount,
                description,
                transaction_type
            )
            VALUES (
                v_external_wallet_id,
                -v_external_balance,
                'Transfer to linked web account',
                'transfer'
            );
        END IF;
        
        RETURN TRUE;
    EXCEPTION
        WHEN OTHERS THEN
            RAISE;
    END;
END;
$$;


ALTER FUNCTION "public"."link_external_identity_to_user"("p_user_id" "uuid", "p_external_identity_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_bot_message"("p_conversation_id" "uuid", "p_content" "text" DEFAULT NULL::"text", "p_image_id" "uuid" DEFAULT NULL::"uuid") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_message_id UUID;
BEGIN
    -- Validate message content
    IF p_content IS NULL AND p_image_id IS NULL THEN
        RAISE EXCEPTION 'Message must have either text content or an image';
    END IF;
    
    -- Insert message
    INSERT INTO public.messages (
        conversation_id,
        sender,
        content,
        image_id
    )
    VALUES (
        p_conversation_id,
        'bot',
        p_content,
        p_image_id
    )
    RETURNING id INTO v_message_id;
    
    RETURN v_message_id;
END;
$$;


ALTER FUNCTION "public"."record_bot_message"("p_conversation_id" "uuid", "p_content" "text", "p_image_id" "uuid") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_file_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb", "p_sender" "text" DEFAULT 'user'::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_upload_id UUID;
BEGIN
  -- Validate sender
  IF p_sender != 'user' AND p_sender != 'bot' THEN
    RAISE EXCEPTION 'Invalid sender. Must be either ''user'' or ''bot''';
  END IF;

  -- Insert the upload record
  INSERT INTO public.user_uploads (
    conversation_id,
    external_identity_id,
    file_url,
    metadata,
    sender
  )
  VALUES (
    p_conversation_id,
    p_external_identity_id,
    p_file_url,
    p_metadata,
    p_sender
  )
  RETURNING id INTO v_upload_id;
  
  RETURN v_upload_id;
END;
$$;


ALTER FUNCTION "public"."record_file_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."record_file_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") IS 'Records a file upload with sender information';



CREATE OR REPLACE FUNCTION "public"."record_user_message"("p_conversation_id" "uuid", "p_content" "text", "p_upload_id" "uuid" DEFAULT NULL::"uuid", "p_sender" "text" DEFAULT 'user'::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_message_id uuid;
BEGIN
  -- Insert message with the given conversation_id, content, and upload_id
  INSERT INTO messages (conversation_id, content, upload_id, sender)
  VALUES (p_conversation_id, p_content, p_upload_id, p_sender)
  RETURNING id INTO v_message_id;
  
  RETURN v_message_id;
END;
$$;


ALTER FUNCTION "public"."record_user_message"("p_conversation_id" "uuid", "p_content" "text", "p_upload_id" "uuid", "p_sender" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb" DEFAULT NULL::"jsonb") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_upload_id UUID;
BEGIN
    -- Insert upload
    INSERT INTO public.user_uploads (
        conversation_id,
        external_identity_id,
        file_url,
        metadata
    )
    VALUES (
        p_conversation_id,
        p_external_identity_id,
        p_file_url,
        p_metadata
    )
    RETURNING id INTO v_upload_id;
    
    RETURN v_upload_id;
END;
$$;


ALTER FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text" DEFAULT 'user'::"text") RETURNS "uuid"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_upload_id UUID;
BEGIN
  -- Insert the upload record with the new sender parameter
  INSERT INTO user_uploads (
    conversation_id,
    external_identity_id,
    file_url,
    metadata,
    sender
  ) VALUES (
    p_conversation_id,
    p_external_identity_id,
    p_file_url,
    p_metadata,
    p_sender
  )
  RETURNING id INTO v_upload_id;
  
  RETURN v_upload_id;
END;
$$;


ALTER FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."sync_tokens_to_profile"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
BEGIN
    -- Update profile tokens when wallet balance changes
    IF NEW.balance != OLD.balance AND NEW.user_id IS NOT NULL THEN
        UPDATE public.profiles
        SET tokens = NEW.balance
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."sync_tokens_to_profile"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_chat_phase_if_conditions_met"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  -- Only consider chats in phase 1.
  IF (SELECT phase FROM chats WHERE id = NEW.chat_id) = 1 THEN
    -- Check if there's at least one reply with non-empty text
    -- and at least one reply with a non-null image_url.
    IF EXISTS (
         SELECT 1
         FROM chat_replies 
         WHERE chat_id = NEW.chat_id 
           AND message IS NOT NULL 
           AND TRIM(message) <> ''
       )
       AND EXISTS (
         SELECT 1
         FROM chat_replies 
         WHERE chat_id = NEW.chat_id 
           AND image_url IS NOT NULL
       ) THEN
         -- Upgrade chat phase to 2.
         UPDATE chats 
         SET phase = 2 
         WHERE id = NEW.chat_id;
        -- Insert a system reply notifying the user.
        INSERT INTO chat_replies (chat_id, sender_role, message)
        VALUES (NEW.chat_id, 'system', 'Thank you for telling us what you require for your video. We will process your request and get back to you');
    END IF;
  END IF;
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_chat_phase_if_conditions_met"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_logo_generations_updated_at"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_logo_generations_updated_at"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."update_updated_at_column"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."update_updated_at_column"() OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."bot_conversations" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "external_identity_id" "uuid" NOT NULL,
    "bot_id" "uuid" NOT NULL,
    "provider" "text" NOT NULL,
    "provider_conversation_id" "text",
    "started_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "valid_conversation_provider" CHECK (("provider" = ANY (ARRAY['telegram'::"text", 'whatsapp'::"text"])))
);


ALTER TABLE "public"."bot_conversations" OWNER TO "postgres";


COMMENT ON TABLE "public"."bot_conversations" IS 'Tracks chat sessions between external users and bot instances';



CREATE TABLE IF NOT EXISTS "public"."external_identities" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "telegram_id" "text",
    "whatsapp_id" "text",
    "telegram_airdrop_granted" boolean DEFAULT false NOT NULL,
    "whatsapp_airdrop_granted" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "telegram_first_name" "text",
    "telegram_last_name" "text",
    "whatsapp_name" "text",
    CONSTRAINT "external_identities_at_least_one_id" CHECK ((("telegram_id" IS NOT NULL) OR ("whatsapp_id" IS NOT NULL)))
);


ALTER TABLE "public"."external_identities" OWNER TO "postgres";


COMMENT ON TABLE "public"."external_identities" IS 'Stores external user identities from messaging platforms like Telegram and WhatsApp';



CREATE TABLE IF NOT EXISTS "public"."messages" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid" NOT NULL,
    "sender" "text" NOT NULL,
    "content" "text",
    "image_id" "uuid",
    "upload_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "single_image_reference" CHECK ((("image_id" IS NULL) OR ("upload_id" IS NULL) OR (("image_id" IS NULL) AND ("upload_id" IS NULL)))),
    CONSTRAINT "valid_message_content" CHECK ((("content" IS NOT NULL) OR ("image_id" IS NOT NULL) OR ("upload_id" IS NOT NULL))),
    CONSTRAINT "valid_sender" CHECK (("sender" = ANY (ARRAY['user'::"text", 'bot'::"text"])))
);


ALTER TABLE "public"."messages" OWNER TO "postgres";


COMMENT ON TABLE "public"."messages" IS 'Logs every message exchanged in a conversation';



CREATE TABLE IF NOT EXISTS "public"."profiles" (
    "user_id" "uuid" NOT NULL,
    "tokens" integer DEFAULT 100 NOT NULL,
    "role" "text" DEFAULT 'user'::"text" NOT NULL,
    "email" "text",
    CONSTRAINT "chk_role" CHECK (("role" = ANY (ARRAY['user'::"text", 'moderator'::"text", 'admin'::"text"])))
);


ALTER TABLE "public"."profiles" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."wallets" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid",
    "external_identity_id" "uuid",
    "balance" integer DEFAULT 0 NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "wallet_single_owner" CHECK (((("user_id" IS NULL) AND ("external_identity_id" IS NOT NULL)) OR (("user_id" IS NOT NULL) AND ("external_identity_id" IS NULL))))
);


ALTER TABLE "public"."wallets" OWNER TO "postgres";


COMMENT ON TABLE "public"."wallets" IS 'Stores token balances for users and external identities';



CREATE OR REPLACE VIEW "public"."admin_users_view" AS
 SELECT "ei"."id" AS "user_id",
    NULL::"uuid" AS "web_user_id",
    "ei"."telegram_id",
    "ei"."whatsapp_id",
    NULL::"text" AS "email",
        CASE
            WHEN ("ei"."telegram_id" IS NOT NULL) THEN 'telegram'::"text"
            ELSE 'whatsapp'::"text"
        END AS "platform",
    COALESCE("w"."balance", 0) AS "balance",
    ( SELECT "count"(*) AS "count"
           FROM ("public"."messages" "m"
             JOIN "public"."bot_conversations" "bc" ON (("m"."conversation_id" = "bc"."id")))
          WHERE ("bc"."external_identity_id" = "ei"."id")) AS "message_count",
    ( SELECT "max"("m"."created_at") AS "max"
           FROM ("public"."messages" "m"
             JOIN "public"."bot_conversations" "bc" ON (("m"."conversation_id" = "bc"."id")))
          WHERE ("bc"."external_identity_id" = "ei"."id")) AS "last_active"
   FROM ("public"."external_identities" "ei"
     LEFT JOIN "public"."wallets" "w" ON (("w"."external_identity_id" = "ei"."id")))
UNION ALL
 SELECT NULL::"uuid" AS "user_id",
    "u"."id" AS "web_user_id",
    NULL::"text" AS "telegram_id",
    NULL::"text" AS "whatsapp_id",
    "u"."email",
    'web'::"text" AS "platform",
    COALESCE("p"."tokens", 0) AS "balance",
    ( SELECT "count"(*) AS "count"
           FROM (("public"."messages" "m"
             JOIN "public"."bot_conversations" "bc" ON (("m"."conversation_id" = "bc"."id")))
             JOIN "public"."external_identities" "ei" ON (("bc"."external_identity_id" = "ei"."id")))
          WHERE ("ei"."user_id" = "u"."id")) AS "message_count",
    ( SELECT "max"("m"."created_at") AS "max"
           FROM (("public"."messages" "m"
             JOIN "public"."bot_conversations" "bc" ON (("m"."conversation_id" = "bc"."id")))
             JOIN "public"."external_identities" "ei" ON (("bc"."external_identity_id" = "ei"."id")))
          WHERE ("ei"."user_id" = "u"."id")) AS "last_active"
   FROM ("auth"."users" "u"
     LEFT JOIN "public"."profiles" "p" ON (("p"."user_id" = "u"."id")));


ALTER TABLE "public"."admin_users_view" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."bots" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "provider" "text" NOT NULL,
    "provider_bot_id" "text" NOT NULL,
    "name" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "valid_provider" CHECK (("provider" = ANY (ARRAY['telegram'::"text", 'whatsapp'::"text"])))
);


ALTER TABLE "public"."bots" OWNER TO "postgres";


COMMENT ON TABLE "public"."bots" IS 'Represents each messaging bot instance across different providers';



CREATE TABLE IF NOT EXISTS "public"."chat_replies" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "chat_id" "uuid" NOT NULL,
    "sender_role" "text" NOT NULL,
    "message" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "image_url" "text",
    "user_id" "uuid",
    "allow_starting_image" boolean DEFAULT false
);


ALTER TABLE "public"."chat_replies" OWNER TO "postgres";


CREATE OR REPLACE VIEW "public"."chat_replies_with_user" AS
 SELECT "cr"."id",
    "cr"."chat_id",
    "cr"."sender_role",
    "cr"."message",
    "cr"."created_at",
    "cr"."image_url",
    "cr"."user_id",
    "u"."email" AS "user_email"
   FROM ("public"."chat_replies" "cr"
     LEFT JOIN "auth"."users" "u" ON (("cr"."user_id" = "u"."id")));


ALTER TABLE "public"."chat_replies_with_user" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."chats" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "start_time" timestamp with time zone DEFAULT "now"(),
    "phase" integer DEFAULT 1 NOT NULL,
    "starting_image_reply_id" "uuid",
    "starting_image_id" "uuid",
    CONSTRAINT "chk_phase" CHECK (("phase" = ANY (ARRAY[1, 2, 3, 4, 5])))
);


ALTER TABLE "public"."chats" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."config_options" (
    "key" "text" NOT NULL,
    "value" "jsonb" NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."config_options" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."free_generations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "status" "text" DEFAULT 'new'::"text" NOT NULL,
    "flow_type" "text" DEFAULT 'mortal_kombat'::"text" NOT NULL,
    "web_generation_request_id" "uuid",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."free_generations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."generated_videos" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "wallet_id" "uuid",
    "request_id" "uuid",
    "video_url" "text" NOT NULL,
    "duration" integer NOT NULL,
    "resolution" character varying(10) NOT NULL,
    "prompt_text" "text" NOT NULL,
    "source_image_url" "text" NOT NULL,
    "fal_request_id" "text",
    "fal_model" character varying(100) DEFAULT 'fal-ai/bytedance/seedance/v1/pro/image-to-video'::character varying NOT NULL,
    "token_cost" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."generated_videos" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."image_to_video_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "wallet_id" "uuid",
    "status" "text" NOT NULL,
    "image_url" "text" NOT NULL,
    "prompt" "text",
    "duration" integer,
    "resolution" "text",
    "video_url" "text",
    "error_message" "text",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()) NOT NULL,
    "estimated_cost" numeric,
    "token_cost" integer,
    "fal_request_id" "text",
    "is_public" boolean DEFAULT false
);


ALTER TABLE "public"."image_to_video_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."images" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "wallet_id" "uuid" NOT NULL,
    "conversation_id" "uuid" NOT NULL,
    "source_type" "text" NOT NULL,
    "prompt_text" "text",
    "parameters" "jsonb",
    "original_upload_id" "uuid",
    "result_url" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "platform" "text" DEFAULT 'bot'::"text",
    "web_request_id" "uuid",
    CONSTRAINT "images_platform_check" CHECK (("platform" = ANY (ARRAY['web'::"text", 'bot'::"text"]))),
    CONSTRAINT "valid_image_source" CHECK (((("source_type" = 'generated'::"text") AND ("prompt_text" IS NOT NULL)) OR (("source_type" = 'edited'::"text") AND ("original_upload_id" IS NOT NULL)))),
    CONSTRAINT "valid_source_type" CHECK (("source_type" = ANY (ARRAY['generated'::"text", 'edited'::"text"])))
);


ALTER TABLE "public"."images" OWNER TO "postgres";


COMMENT ON TABLE "public"."images" IS 'Records every generated or edited image';



CREATE TABLE IF NOT EXISTS "public"."link_codes" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "code" character varying(16) NOT NULL,
    "user_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "used" boolean DEFAULT false NOT NULL,
    "external_identity_id" "uuid",
    "expires_at" timestamp with time zone DEFAULT ("now"() + '00:30:00'::interval) NOT NULL
);


ALTER TABLE "public"."link_codes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."logo_generations" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "prompt" "text" NOT NULL,
    "style" character varying(50) NOT NULL,
    "image_url" "text" NOT NULL,
    "fal_request_id" "text",
    "model_used" character varying(100),
    "token_cost" integer DEFAULT 15,
    "status" character varying(20) DEFAULT 'pending'::character varying,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."logo_generations" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payments" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "token_package_id" "uuid" NOT NULL,
    "stripe_payment_intent_id" "text",
    "stripe_session_id" "text",
    "stripe_customer_id" "text",
    "amount_cents" integer NOT NULL,
    "currency" "text" DEFAULT 'USD'::"text" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text" NOT NULL,
    "tokens_granted" integer,
    "payment_method" "text",
    "metadata" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "payments_amount_cents_check" CHECK (("amount_cents" > 0)),
    CONSTRAINT "payments_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'processing'::"text", 'completed'::"text", 'failed'::"text", 'cancelled'::"text", 'refunded'::"text"]))),
    CONSTRAINT "payments_tokens_granted_check" CHECK (("tokens_granted" >= 0))
);


ALTER TABLE "public"."payments" OWNER TO "postgres";


COMMENT ON TABLE "public"."payments" IS 'Tracks all payment transactions for token purchases';



COMMENT ON COLUMN "public"."payments"."stripe_payment_intent_id" IS 'Stripe PaymentIntent ID for tracking';



COMMENT ON COLUMN "public"."payments"."stripe_session_id" IS 'Stripe Checkout Session ID if using Stripe Checkout';



COMMENT ON COLUMN "public"."payments"."stripe_customer_id" IS 'Stripe Customer ID for the user';



COMMENT ON COLUMN "public"."payments"."amount_cents" IS 'Amount paid in cents';



COMMENT ON COLUMN "public"."payments"."tokens_granted" IS 'Number of tokens actually granted to user wallet';



COMMENT ON COLUMN "public"."payments"."metadata" IS 'Additional payment metadata from Stripe';



CREATE TABLE IF NOT EXISTS "public"."token_packages" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "token_amount" integer NOT NULL,
    "price_cents" integer NOT NULL,
    "currency" "text" DEFAULT 'USD'::"text" NOT NULL,
    "stripe_price_id" "text",
    "is_active" boolean DEFAULT true NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "token_packages_price_cents_check" CHECK (("price_cents" > 0)),
    CONSTRAINT "token_packages_token_amount_check" CHECK (("token_amount" > 0))
);


ALTER TABLE "public"."token_packages" OWNER TO "postgres";


COMMENT ON TABLE "public"."token_packages" IS 'Defines different token packages available for purchase';



COMMENT ON COLUMN "public"."token_packages"."token_amount" IS 'Number of tokens included in this package';



COMMENT ON COLUMN "public"."token_packages"."price_cents" IS 'Price in cents to avoid floating point issues';



COMMENT ON COLUMN "public"."token_packages"."stripe_price_id" IS 'Stripe Price ID for this package';



CREATE TABLE IF NOT EXISTS "public"."token_transactions" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "wallet_id" "uuid" NOT NULL,
    "amount" integer NOT NULL,
    "description" "text" NOT NULL,
    "transaction_type" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "valid_transaction_type" CHECK (("transaction_type" = ANY (ARRAY['airdrop'::"text", 'consumption'::"text", 'transfer'::"text", 'refund'::"text", 'manual_adjustment'::"text", 'purchase'::"text"])))
);


ALTER TABLE "public"."token_transactions" OWNER TO "postgres";


COMMENT ON TABLE "public"."token_transactions" IS 'Tracks all token transactions including airdrops, consumption, and transfers';



CREATE TABLE IF NOT EXISTS "public"."tools" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "category" "text",
    "icon" "text",
    "route" "text",
    "status" "text" DEFAULT 'active'::"text" NOT NULL,
    "features" "jsonb",
    "order_index" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"()
);


ALTER TABLE "public"."tools" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."user_uploads" (
    "id" "uuid" DEFAULT "extensions"."uuid_generate_v4"() NOT NULL,
    "conversation_id" "uuid" NOT NULL,
    "external_identity_id" "uuid" NOT NULL,
    "file_url" "text" NOT NULL,
    "metadata" "jsonb",
    "uploaded_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "sender" "text" DEFAULT 'user'::"text" NOT NULL,
    CONSTRAINT "user_uploads_sender_check" CHECK (("sender" = ANY (ARRAY['user'::"text", 'bot'::"text"])))
);


ALTER TABLE "public"."user_uploads" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_uploads" IS 'Stores each image file that a user uploads for editing or reference';



COMMENT ON COLUMN "public"."user_uploads"."sender" IS 'Indicates who sent the file, either ''user'' or ''bot''';



CREATE TABLE IF NOT EXISTS "public"."web_generation_requests" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "wallet_id" "uuid",
    "flow_type" "text" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text",
    "current_step" integer DEFAULT 1,
    "total_steps" integer DEFAULT 4 NOT NULL,
    "awaiting_approval_for_step" integer,
    "approved_steps" integer[] DEFAULT '{}'::integer[],
    "rejected_steps" integer[] DEFAULT '{}'::integer[],
    "estimated_cost" numeric(10,4),
    "actual_cost" numeric(10,4) DEFAULT 0,
    "input_data" "jsonb" NOT NULL,
    "output_data" "jsonb" DEFAULT '{}'::"jsonb",
    "error_message" "text",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "updated_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    "is_public" boolean DEFAULT false,
    "fal_model" "text",
    CONSTRAINT "web_generation_requests_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'awaiting_approval'::"text", 'approved'::"text", 'rejected'::"text", 'in_progress'::"text", 'completed'::"text", 'failed'::"text"])))
);


ALTER TABLE "public"."web_generation_requests" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."web_generation_steps" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "web_request_id" "uuid" NOT NULL,
    "step_number" integer NOT NULL,
    "step_type" "text" NOT NULL,
    "status" "text" DEFAULT 'pending'::"text",
    "fal_model" "text",
    "fal_request_id" "text",
    "input_data" "jsonb",
    "output_data" "jsonb",
    "requires_approval" boolean DEFAULT false,
    "approved_at" timestamp with time zone,
    "approved_by" "uuid",
    "rejection_reason" "text",
    "started_at" timestamp with time zone,
    "completed_at" timestamp with time zone,
    "token_cost" integer DEFAULT 0,
    "error_message" "text",
    "retry_count" integer DEFAULT 0,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    CONSTRAINT "web_generation_steps_status_check" CHECK (("status" = ANY (ARRAY['pending'::"text", 'in_progress'::"text", 'completed'::"text", 'failed'::"text", 'awaiting_approval'::"text", 'approved'::"text", 'rejected'::"text"]))),
    CONSTRAINT "web_generation_steps_step_type_check" CHECK (("step_type" = ANY (ARRAY['image_upload'::"text", 'image_transform'::"text", 'user_approval'::"text", 'video_generate'::"text"])))
);


ALTER TABLE "public"."web_generation_steps" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."web_image_edits" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid",
    "wallet_id" "uuid",
    "status" character varying(50) DEFAULT 'pending'::character varying,
    "original_image_url" "text" NOT NULL,
    "prompt" "text" NOT NULL,
    "guidance_scale" numeric(3,1) DEFAULT 3.5,
    "safety_tolerance" character varying(2) DEFAULT '2'::character varying,
    "output_format" character varying(10) DEFAULT 'jpeg'::character varying,
    "seed" integer,
    "edited_image_url" "text",
    "fal_request_id" "text",
    "token_cost" integer DEFAULT 10,
    "is_public" boolean DEFAULT false,
    "error_message" "text",
    "created_at" timestamp with time zone DEFAULT "now"(),
    "updated_at" timestamp with time zone DEFAULT "now"(),
    "completed_at" timestamp with time zone,
    CONSTRAINT "web_image_edits_output_format_check" CHECK ((("output_format")::"text" = ANY ((ARRAY['jpeg'::character varying, 'png'::character varying])::"text"[]))),
    CONSTRAINT "web_image_edits_status_check" CHECK ((("status")::"text" = ANY ((ARRAY['pending'::character varying, 'processing'::character varying, 'completed'::character varying, 'failed'::character varying])::"text"[])))
);


ALTER TABLE "public"."web_image_edits" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."web_images" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "wallet_id" "uuid" NOT NULL,
    "web_request_id" "uuid",
    "original_image_url" "text",
    "result_image_url" "text" NOT NULL,
    "prompt_text" "text",
    "parameters" "jsonb",
    "fal_request_id" "text",
    "fal_model" "text" DEFAULT 'flux-pro/kontext'::"text" NOT NULL,
    "token_cost" integer DEFAULT 10 NOT NULL,
    "platform" "text" DEFAULT 'web'::"text",
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"()),
    CONSTRAINT "web_images_platform_check" CHECK (("platform" = ANY (ARRAY['web'::"text", 'bot'::"text"])))
);


ALTER TABLE "public"."web_images" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."web_videos" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "wallet_id" "uuid" NOT NULL,
    "web_request_id" "uuid" NOT NULL,
    "video_url" "text" NOT NULL,
    "duration" integer,
    "resolution" "text",
    "file_size" bigint,
    "prompt_text" "text",
    "parameters" "jsonb",
    "source_image_urls" "text"[],
    "source_video_url" "text",
    "fal_request_id" "text",
    "fal_model" "text" NOT NULL,
    "token_cost" integer NOT NULL,
    "created_at" timestamp with time zone DEFAULT "timezone"('utc'::"text", "now"())
);


ALTER TABLE "public"."web_videos" OWNER TO "postgres";


ALTER TABLE ONLY "public"."bot_conversations"
    ADD CONSTRAINT "bot_conversations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."bots"
    ADD CONSTRAINT "bots_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chat_replies"
    ADD CONSTRAINT "chat_replies_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."chats"
    ADD CONSTRAINT "chats_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."config_options"
    ADD CONSTRAINT "config_options_pkey" PRIMARY KEY ("key");



ALTER TABLE ONLY "public"."external_identities"
    ADD CONSTRAINT "external_identities_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."free_generations"
    ADD CONSTRAINT "free_generations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."generated_videos"
    ADD CONSTRAINT "generated_videos_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."image_to_video_requests"
    ADD CONSTRAINT "image_to_video_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."link_codes"
    ADD CONSTRAINT "link_codes_code_key" UNIQUE ("code");



ALTER TABLE ONLY "public"."link_codes"
    ADD CONSTRAINT "link_codes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."logo_generations"
    ADD CONSTRAINT "logo_generations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_stripe_payment_intent_id_key" UNIQUE ("stripe_payment_intent_id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_pkey" PRIMARY KEY ("user_id");



ALTER TABLE ONLY "public"."token_packages"
    ADD CONSTRAINT "token_packages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."token_packages"
    ADD CONSTRAINT "token_packages_stripe_price_id_key" UNIQUE ("stripe_price_id");



ALTER TABLE ONLY "public"."token_transactions"
    ADD CONSTRAINT "token_transactions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tools"
    ADD CONSTRAINT "tools_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."bots"
    ADD CONSTRAINT "unique_provider_bot" UNIQUE ("provider", "provider_bot_id");



ALTER TABLE ONLY "public"."bot_conversations"
    ADD CONSTRAINT "unique_provider_conversation" UNIQUE ("provider", "provider_conversation_id");



ALTER TABLE ONLY "public"."external_identities"
    ADD CONSTRAINT "unique_telegram_id" UNIQUE ("telegram_id");



ALTER TABLE ONLY "public"."external_identities"
    ADD CONSTRAINT "unique_whatsapp_id" UNIQUE ("whatsapp_id");



ALTER TABLE ONLY "public"."user_uploads"
    ADD CONSTRAINT "user_uploads_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."wallets"
    ADD CONSTRAINT "wallets_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."web_generation_requests"
    ADD CONSTRAINT "web_generation_requests_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."web_generation_steps"
    ADD CONSTRAINT "web_generation_steps_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."web_image_edits"
    ADD CONSTRAINT "web_image_edits_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."web_images"
    ADD CONSTRAINT "web_images_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."web_videos"
    ADD CONSTRAINT "web_videos_pkey" PRIMARY KEY ("id");



CREATE INDEX "bot_conversations_bot_id_idx" ON "public"."bot_conversations" USING "btree" ("bot_id");



CREATE INDEX "bot_conversations_external_identity_id_idx" ON "public"."bot_conversations" USING "btree" ("external_identity_id");



CREATE INDEX "bot_conversations_provider_idx" ON "public"."bot_conversations" USING "btree" ("provider");



CREATE INDEX "bots_provider_idx" ON "public"."bots" USING "btree" ("provider");



CREATE INDEX "idx_generated_videos_created_at" ON "public"."generated_videos" USING "btree" ("created_at");



CREATE INDEX "idx_generated_videos_request_id" ON "public"."generated_videos" USING "btree" ("request_id");



CREATE INDEX "idx_generated_videos_user_id" ON "public"."generated_videos" USING "btree" ("user_id");



CREATE INDEX "idx_image_to_video_requests_fal_request_id" ON "public"."image_to_video_requests" USING "btree" ("fal_request_id");



CREATE INDEX "idx_image_to_video_requests_is_public" ON "public"."image_to_video_requests" USING "btree" ("is_public");



CREATE INDEX "idx_image_to_video_requests_public_completed" ON "public"."image_to_video_requests" USING "btree" ("is_public", "status") WHERE ("status" = 'completed'::"text");



CREATE INDEX "idx_link_codes_code" ON "public"."link_codes" USING "btree" ("code");



CREATE INDEX "idx_link_codes_external_identity_id" ON "public"."link_codes" USING "btree" ("external_identity_id");



CREATE INDEX "idx_link_codes_user_id" ON "public"."link_codes" USING "btree" ("user_id");



CREATE INDEX "idx_logo_generations_created_at" ON "public"."logo_generations" USING "btree" ("created_at" DESC);



CREATE INDEX "idx_logo_generations_status" ON "public"."logo_generations" USING "btree" ("status");



CREATE INDEX "idx_logo_generations_user_id" ON "public"."logo_generations" USING "btree" ("user_id");



CREATE INDEX "idx_payments_status" ON "public"."payments" USING "btree" ("status", "created_at" DESC);



CREATE INDEX "idx_payments_stripe_payment_intent" ON "public"."payments" USING "btree" ("stripe_payment_intent_id");



CREATE INDEX "idx_payments_stripe_session" ON "public"."payments" USING "btree" ("stripe_session_id");



CREATE INDEX "idx_payments_user_id" ON "public"."payments" USING "btree" ("user_id", "created_at" DESC);



CREATE INDEX "idx_token_packages_active" ON "public"."token_packages" USING "btree" ("is_active", "created_at") WHERE ("is_active" = true);



CREATE INDEX "idx_tools_order" ON "public"."tools" USING "btree" ("order_index");



CREATE INDEX "idx_tools_status" ON "public"."tools" USING "btree" ("status");



CREATE INDEX "idx_user_uploads_sender" ON "public"."user_uploads" USING "btree" ("sender");



CREATE INDEX "idx_web_generation_requests_flow_type" ON "public"."web_generation_requests" USING "btree" ("flow_type");



CREATE INDEX "idx_web_generation_requests_status" ON "public"."web_generation_requests" USING "btree" ("status");



CREATE INDEX "idx_web_generation_requests_user_id" ON "public"."web_generation_requests" USING "btree" ("user_id");



CREATE INDEX "idx_web_generation_steps_request_id" ON "public"."web_generation_steps" USING "btree" ("web_request_id");



CREATE INDEX "idx_web_generation_steps_step_number" ON "public"."web_generation_steps" USING "btree" ("web_request_id", "step_number");



CREATE INDEX "idx_web_image_edits_created_at" ON "public"."web_image_edits" USING "btree" ("created_at");



CREATE INDEX "idx_web_image_edits_is_public" ON "public"."web_image_edits" USING "btree" ("is_public");



CREATE INDEX "idx_web_image_edits_status" ON "public"."web_image_edits" USING "btree" ("status");



CREATE INDEX "idx_web_image_edits_user_id" ON "public"."web_image_edits" USING "btree" ("user_id");



CREATE INDEX "idx_web_images_created_at" ON "public"."web_images" USING "btree" ("created_at" DESC);



CREATE INDEX "idx_web_images_user_id" ON "public"."web_images" USING "btree" ("user_id");



CREATE INDEX "idx_web_images_web_request_id" ON "public"."web_images" USING "btree" ("web_request_id");



CREATE INDEX "idx_web_videos_request_id" ON "public"."web_videos" USING "btree" ("web_request_id");



CREATE INDEX "images_conversation_id_idx" ON "public"."images" USING "btree" ("conversation_id");



CREATE INDEX "images_original_upload_id_idx" ON "public"."images" USING "btree" ("original_upload_id") WHERE ("original_upload_id" IS NOT NULL);



CREATE INDEX "images_wallet_id_idx" ON "public"."images" USING "btree" ("wallet_id");



CREATE INDEX "messages_conversation_id_idx" ON "public"."messages" USING "btree" ("conversation_id");



CREATE INDEX "messages_image_id_idx" ON "public"."messages" USING "btree" ("image_id") WHERE ("image_id" IS NOT NULL);



CREATE INDEX "messages_upload_id_idx" ON "public"."messages" USING "btree" ("upload_id") WHERE ("upload_id" IS NOT NULL);



CREATE INDEX "token_transactions_wallet_id_idx" ON "public"."token_transactions" USING "btree" ("wallet_id");



CREATE INDEX "user_uploads_conversation_id_idx" ON "public"."user_uploads" USING "btree" ("conversation_id");



CREATE INDEX "user_uploads_external_identity_id_idx" ON "public"."user_uploads" USING "btree" ("external_identity_id");



CREATE INDEX "wallets_external_identity_id_idx" ON "public"."wallets" USING "btree" ("external_identity_id") WHERE ("external_identity_id" IS NOT NULL);



CREATE INDEX "wallets_user_id_idx" ON "public"."wallets" USING "btree" ("user_id") WHERE ("user_id" IS NOT NULL);



CREATE OR REPLACE TRIGGER "update_logo_generations_updated_at" BEFORE UPDATE ON "public"."logo_generations" FOR EACH ROW EXECUTE FUNCTION "public"."update_logo_generations_updated_at"();



CREATE OR REPLACE TRIGGER "update_payments_updated_at" BEFORE UPDATE ON "public"."payments" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "update_token_packages_updated_at" BEFORE UPDATE ON "public"."token_packages" FOR EACH ROW EXECUTE FUNCTION "public"."update_updated_at_column"();



CREATE OR REPLACE TRIGGER "wallet_balance_changed" AFTER UPDATE OF "balance" ON "public"."wallets" FOR EACH ROW WHEN (("new"."balance" IS DISTINCT FROM "old"."balance")) EXECUTE FUNCTION "public"."sync_tokens_to_profile"();



ALTER TABLE ONLY "public"."bot_conversations"
    ADD CONSTRAINT "bot_conversations_bot_id_fkey" FOREIGN KEY ("bot_id") REFERENCES "public"."bots"("id");



ALTER TABLE ONLY "public"."bot_conversations"
    ADD CONSTRAINT "bot_conversations_external_identity_id_fkey" FOREIGN KEY ("external_identity_id") REFERENCES "public"."external_identities"("id");



ALTER TABLE ONLY "public"."chat_replies"
    ADD CONSTRAINT "chat_replies_chat_id_fkey" FOREIGN KEY ("chat_id") REFERENCES "public"."chats"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."chat_replies"
    ADD CONSTRAINT "chat_replies_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."chats"
    ADD CONSTRAINT "chats_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."external_identities"
    ADD CONSTRAINT "external_identities_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."chats"
    ADD CONSTRAINT "fk_starting_image" FOREIGN KEY ("starting_image_id") REFERENCES "public"."chat_replies"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."free_generations"
    ADD CONSTRAINT "free_generations_web_generation_request_id_fkey" FOREIGN KEY ("web_generation_request_id") REFERENCES "public"."web_generation_requests"("id");



ALTER TABLE ONLY "public"."generated_videos"
    ADD CONSTRAINT "generated_videos_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "public"."image_to_video_requests"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."generated_videos"
    ADD CONSTRAINT "generated_videos_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."generated_videos"
    ADD CONSTRAINT "generated_videos_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."image_to_video_requests"
    ADD CONSTRAINT "image_to_video_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."image_to_video_requests"
    ADD CONSTRAINT "image_to_video_requests_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."bot_conversations"("id");



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_original_upload_id_fkey" FOREIGN KEY ("original_upload_id") REFERENCES "public"."user_uploads"("id");



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id");



ALTER TABLE ONLY "public"."images"
    ADD CONSTRAINT "images_web_request_id_fkey" FOREIGN KEY ("web_request_id") REFERENCES "public"."web_generation_requests"("id");



ALTER TABLE ONLY "public"."link_codes"
    ADD CONSTRAINT "link_codes_external_identity_id_fkey" FOREIGN KEY ("external_identity_id") REFERENCES "public"."external_identities"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."link_codes"
    ADD CONSTRAINT "link_codes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."logo_generations"
    ADD CONSTRAINT "logo_generations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."bot_conversations"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "public"."images"("id");



ALTER TABLE ONLY "public"."messages"
    ADD CONSTRAINT "messages_upload_id_fkey" FOREIGN KEY ("upload_id") REFERENCES "public"."user_uploads"("id");



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_token_package_id_fkey" FOREIGN KEY ("token_package_id") REFERENCES "public"."token_packages"("id");



ALTER TABLE ONLY "public"."payments"
    ADD CONSTRAINT "payments_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."profiles"
    ADD CONSTRAINT "profiles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."token_transactions"
    ADD CONSTRAINT "token_transactions_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id");



ALTER TABLE ONLY "public"."user_uploads"
    ADD CONSTRAINT "user_uploads_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "public"."bot_conversations"("id");



ALTER TABLE ONLY "public"."user_uploads"
    ADD CONSTRAINT "user_uploads_external_identity_id_fkey" FOREIGN KEY ("external_identity_id") REFERENCES "public"."external_identities"("id");



ALTER TABLE ONLY "public"."wallets"
    ADD CONSTRAINT "wallets_external_identity_id_fkey" FOREIGN KEY ("external_identity_id") REFERENCES "public"."external_identities"("id");



ALTER TABLE ONLY "public"."wallets"
    ADD CONSTRAINT "wallets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."web_generation_requests"
    ADD CONSTRAINT "web_generation_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."web_generation_requests"
    ADD CONSTRAINT "web_generation_requests_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id");



ALTER TABLE ONLY "public"."web_generation_steps"
    ADD CONSTRAINT "web_generation_steps_approved_by_fkey" FOREIGN KEY ("approved_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."web_generation_steps"
    ADD CONSTRAINT "web_generation_steps_web_request_id_fkey" FOREIGN KEY ("web_request_id") REFERENCES "public"."web_generation_requests"("id");



ALTER TABLE ONLY "public"."web_image_edits"
    ADD CONSTRAINT "web_image_edits_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."web_image_edits"
    ADD CONSTRAINT "web_image_edits_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."web_images"
    ADD CONSTRAINT "web_images_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."web_images"
    ADD CONSTRAINT "web_images_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id");



ALTER TABLE ONLY "public"."web_images"
    ADD CONSTRAINT "web_images_web_request_id_fkey" FOREIGN KEY ("web_request_id") REFERENCES "public"."web_generation_requests"("id");



ALTER TABLE ONLY "public"."web_videos"
    ADD CONSTRAINT "web_videos_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."web_videos"
    ADD CONSTRAINT "web_videos_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallets"("id");



ALTER TABLE ONLY "public"."web_videos"
    ADD CONSTRAINT "web_videos_web_request_id_fkey" FOREIGN KEY ("web_request_id") REFERENCES "public"."web_generation_requests"("id");



CREATE POLICY "Admins can update all payments" ON "public"."payments" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all logo generations" ON "public"."logo_generations" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Admins can view all payments" ON "public"."payments" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow Admins to Delete Chats" ON "public"."chats" FOR DELETE TO "authenticated" USING ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow Admins to Insert Chats" ON "public"."chats" FOR INSERT TO "authenticated" WITH CHECK ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow Admins to Select Chats" ON "public"."chats" FOR SELECT TO "authenticated" USING ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow Admins to Update Chats" ON "public"."chats" FOR UPDATE TO "authenticated" WITH CHECK ((( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text"));



CREATE POLICY "Allow administrators to manage bots" ON "public"."bots" USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all conversations" ON "public"."bot_conversations" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all external identities" ON "public"."external_identities" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all images" ON "public"."images" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all messages" ON "public"."messages" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all transactions" ON "public"."token_transactions" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all uploads" ON "public"."user_uploads" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow administrators to read all wallets" ON "public"."wallets" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Allow anyone to read bots" ON "public"."bots" FOR SELECT USING (true);



CREATE POLICY "Allow authenticated user to select their chats" ON "public"."chats" FOR SELECT USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow only authenticated user to insert their chat_replies" ON "public"."chat_replies" FOR INSERT WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow only authenticated user to insert their chats" ON "public"."chats" FOR INSERT WITH CHECK (("user_id" = "auth"."uid"()));



CREATE POLICY "Allow users to read their own conversations" ON "public"."bot_conversations" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."external_identities"
  WHERE (("external_identities"."id" = "bot_conversations"."external_identity_id") AND ("external_identities"."user_id" = "auth"."uid"())))));



CREATE POLICY "Allow users to read their own external identities" ON "public"."external_identities" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Allow users to read their own images" ON "public"."images" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."wallets"
  WHERE (("wallets"."id" = "images"."wallet_id") AND ("wallets"."user_id" = "auth"."uid"())))) OR (EXISTS ( SELECT 1
   FROM ("public"."wallets" "w"
     JOIN "public"."external_identities" "e" ON (("w"."external_identity_id" = "e"."id")))
  WHERE (("w"."id" = "images"."wallet_id") AND ("e"."user_id" = "auth"."uid"()))))));



CREATE POLICY "Allow users to read their own messages" ON "public"."messages" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM ("public"."bot_conversations" "c"
     JOIN "public"."external_identities" "e" ON (("c"."external_identity_id" = "e"."id")))
  WHERE (("c"."id" = "messages"."conversation_id") AND ("e"."user_id" = "auth"."uid"())))));



CREATE POLICY "Allow users to read their own transactions" ON "public"."token_transactions" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."wallets"
  WHERE (("wallets"."id" = "token_transactions"."wallet_id") AND ("wallets"."user_id" = "auth"."uid"())))));



CREATE POLICY "Allow users to read their own uploads" ON "public"."user_uploads" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."external_identities"
  WHERE (("external_identities"."id" = "user_uploads"."external_identity_id") AND ("external_identities"."user_id" = "auth"."uid"())))));



CREATE POLICY "Allow users to read their own wallet" ON "public"."wallets" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Anyone can view public image edits" ON "public"."web_image_edits" FOR SELECT USING (("is_public" = true));



CREATE POLICY "Enable insert for authenticated users only" ON "public"."chat_replies" FOR INSERT TO "authenticated" WITH CHECK (true);



CREATE POLICY "Enable read access for all users" ON "public"."chat_replies" FOR SELECT USING (true);



CREATE POLICY "Only admins can insert token packages" ON "public"."token_packages" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Only admins can update token packages" ON "public"."token_packages" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."profiles"
  WHERE (("profiles"."user_id" = "auth"."uid"()) AND ("profiles"."role" = 'admin'::"text")))));



CREATE POLICY "Service role can manage image edits" ON "public"."web_image_edits" USING (("current_setting"('role'::"text") = 'service_role'::"text"));



CREATE POLICY "Token packages are viewable by everyone" ON "public"."token_packages" FOR SELECT USING (true);



CREATE POLICY "Users can create generation requests" ON "public"."web_generation_requests" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can create generation steps" ON "public"."web_generation_steps" FOR INSERT WITH CHECK ((EXISTS ( SELECT 1
   FROM "public"."web_generation_requests"
  WHERE (("web_generation_requests"."id" = "web_generation_steps"."web_request_id") AND ("web_generation_requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can create their own image edits" ON "public"."web_image_edits" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can create their own logo generations" ON "public"."logo_generations" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can create videos" ON "public"."web_videos" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can delete their own external identity" ON "public"."external_identities" FOR DELETE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can insert their own payments" ON "public"."payments" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert their own videos" ON "public"."generated_videos" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can insert their own web images" ON "public"."web_images" FOR INSERT WITH CHECK (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can unlink their own external identity" ON "public"."external_identities" FOR UPDATE USING (("user_id" = "auth"."uid"())) WITH CHECK ((("user_id" = "auth"."uid"()) OR ("user_id" IS NULL)));



CREATE POLICY "Users can update their own external identity" ON "public"."external_identities" FOR UPDATE USING (("user_id" = "auth"."uid"()));



CREATE POLICY "Users can update their own generation requests" ON "public"."web_generation_requests" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own generation steps" ON "public"."web_generation_steps" FOR UPDATE USING ((EXISTS ( SELECT 1
   FROM "public"."web_generation_requests"
  WHERE (("web_generation_requests"."id" = "web_generation_steps"."web_request_id") AND ("web_generation_requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can update their own image edits" ON "public"."web_image_edits" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own logo generations" ON "public"."logo_generations" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own payments" ON "public"."payments" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own videos" ON "public"."generated_videos" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can update their own videos" ON "public"."web_videos" FOR UPDATE USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own generation requests" ON "public"."web_generation_requests" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own generation steps" ON "public"."web_generation_steps" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."web_generation_requests"
  WHERE (("web_generation_requests"."id" = "web_generation_steps"."web_request_id") AND ("web_generation_requests"."user_id" = "auth"."uid"())))));



CREATE POLICY "Users can view their own image edits" ON "public"."web_image_edits" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own logo generations" ON "public"."logo_generations" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own payments" ON "public"."payments" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own videos" ON "public"."generated_videos" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own videos" ON "public"."web_videos" FOR SELECT USING (("auth"."uid"() = "user_id"));



CREATE POLICY "Users can view their own web images" ON "public"."web_images" FOR SELECT USING (("auth"."uid"() = "user_id"));



ALTER TABLE "public"."bot_conversations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."bots" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."chat_replies" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."chats" ENABLE ROW LEVEL SECURITY;


CREATE POLICY "delete_reply_policy" ON "public"."chat_replies" FOR DELETE USING ((("auth"."uid"() = "user_id") OR (( SELECT "profiles"."role"
   FROM "public"."profiles"
  WHERE ("profiles"."user_id" = "auth"."uid"())) = 'admin'::"text")));



ALTER TABLE "public"."external_identities" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."generated_videos" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."images" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."logo_generations" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."messages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."payments" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."token_packages" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."token_transactions" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_uploads" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."wallets" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."web_generation_requests" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."web_generation_steps" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."web_image_edits" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."web_images" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."web_videos" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";






ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."chat_replies";



ALTER PUBLICATION "supabase_realtime" ADD TABLE ONLY "public"."messages";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";











































































































































































GRANT ALL ON FUNCTION "public"."add_test_user"("email" "text", "password" "text", "is_confirmed" boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."add_test_user"("email" "text", "password" "text", "is_confirmed" boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."add_test_user"("email" "text", "password" "text", "is_confirmed" boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."complete_payment"("payment_id_param" "uuid", "stripe_payment_intent_id_param" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."complete_payment"("payment_id_param" "uuid", "stripe_payment_intent_id_param" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."complete_payment"("payment_id_param" "uuid", "stripe_payment_intent_id_param" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."confirm_user"("user_email" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."confirm_user"("user_email" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."confirm_user"("user_email" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."consume_tokens"("p_wallet_id" "uuid", "p_amount" integer, "p_description" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."consume_tokens"("p_wallet_id" "uuid", "p_amount" integer, "p_description" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."consume_tokens"("p_wallet_id" "uuid", "p_amount" integer, "p_description" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."create_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."create_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."deduct_tokens_for_new_chat"() TO "anon";
GRANT ALL ON FUNCTION "public"."deduct_tokens_for_new_chat"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."deduct_tokens_for_new_chat"() TO "service_role";



GRANT ALL ON FUNCTION "public"."find_external_identity_by_id"("p_external_identity_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."find_external_identity_by_id"("p_external_identity_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."find_external_identity_by_id"("p_external_identity_id" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_edited_image"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_original_upload_id" "uuid", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."generate_edited_image"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_original_upload_id" "uuid", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_edited_image"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_original_upload_id" "uuid", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."generate_image_from_text"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_prompt_text" "text", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."generate_image_from_text"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_prompt_text" "text", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."generate_image_from_text"("p_wallet_id" "uuid", "p_conversation_id" "uuid", "p_prompt_text" "text", "p_token_cost" integer, "p_result_url" "text", "p_parameters" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_admin_users_with_activity"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_admin_users_with_activity"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_admin_users_with_activity"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_bot_activity"() TO "anon";
GRANT ALL ON FUNCTION "public"."get_bot_activity"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_bot_activity"() TO "service_role";



GRANT ALL ON FUNCTION "public"."get_or_create_bot_conversation"("p_external_identity_id" "uuid", "p_bot_id" "uuid", "p_provider" "text", "p_provider_conversation_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_or_create_bot_conversation"("p_external_identity_id" "uuid", "p_bot_id" "uuid", "p_provider" "text", "p_provider_conversation_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_or_create_bot_conversation"("p_external_identity_id" "uuid", "p_bot_id" "uuid", "p_provider" "text", "p_provider_conversation_id" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."get_or_create_external_wallet"("p_telegram_id" "text", "p_whatsapp_id" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."get_or_create_external_wallet"("p_telegram_id" "text", "p_whatsapp_id" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."get_or_create_external_wallet"("p_telegram_id" "text", "p_whatsapp_id" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_auth_user_created"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_auth_user_created"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_auth_user_created"() TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_message_from_api"("p_conversation_id" "text", "p_content" "text", "p_upload_id" "text", "p_sender" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."insert_message_from_api"("p_conversation_id" "text", "p_content" "text", "p_upload_id" "text", "p_sender" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_message_from_api"("p_conversation_id" "text", "p_content" "text", "p_upload_id" "text", "p_sender" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_user_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."insert_user_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_user_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."insert_user_upload_from_api"("p_conversation_id" "text", "p_external_identity_id_text" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."insert_user_upload_from_api"("p_conversation_id" "text", "p_external_identity_id_text" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."insert_user_upload_from_api"("p_conversation_id" "text", "p_external_identity_id_text" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."link_external_identity_to_user"("p_user_id" "uuid", "p_external_identity_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."link_external_identity_to_user"("p_user_id" "uuid", "p_external_identity_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."link_external_identity_to_user"("p_user_id" "uuid", "p_external_identity_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_bot_message"("p_conversation_id" "uuid", "p_content" "text", "p_image_id" "uuid") TO "anon";
GRANT ALL ON FUNCTION "public"."record_bot_message"("p_conversation_id" "uuid", "p_content" "text", "p_image_id" "uuid") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_bot_message"("p_conversation_id" "uuid", "p_content" "text", "p_image_id" "uuid") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_file_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."record_file_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_file_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_user_message"("p_conversation_id" "uuid", "p_content" "text", "p_upload_id" "uuid", "p_sender" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."record_user_message"("p_conversation_id" "uuid", "p_content" "text", "p_upload_id" "uuid", "p_sender" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_user_message"("p_conversation_id" "uuid", "p_content" "text", "p_upload_id" "uuid", "p_sender" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb") TO "anon";
GRANT ALL ON FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "uuid", "p_file_url" "text", "p_metadata" "jsonb") TO "service_role";



GRANT ALL ON FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."record_user_upload"("p_conversation_id" "uuid", "p_external_identity_id" "text", "p_file_url" "text", "p_metadata" "jsonb", "p_sender" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."sync_tokens_to_profile"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_tokens_to_profile"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_tokens_to_profile"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_chat_phase_if_conditions_met"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_chat_phase_if_conditions_met"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_chat_phase_if_conditions_met"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_logo_generations_updated_at"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_logo_generations_updated_at"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_logo_generations_updated_at"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_updated_at_column"() TO "service_role";


















GRANT ALL ON TABLE "public"."bot_conversations" TO "anon";
GRANT ALL ON TABLE "public"."bot_conversations" TO "authenticated";
GRANT ALL ON TABLE "public"."bot_conversations" TO "service_role";



GRANT ALL ON TABLE "public"."external_identities" TO "anon";
GRANT ALL ON TABLE "public"."external_identities" TO "authenticated";
GRANT ALL ON TABLE "public"."external_identities" TO "service_role";



GRANT ALL ON TABLE "public"."messages" TO "anon";
GRANT ALL ON TABLE "public"."messages" TO "authenticated";
GRANT ALL ON TABLE "public"."messages" TO "service_role";



GRANT ALL ON TABLE "public"."profiles" TO "anon";
GRANT ALL ON TABLE "public"."profiles" TO "authenticated";
GRANT ALL ON TABLE "public"."profiles" TO "service_role";



GRANT ALL ON TABLE "public"."wallets" TO "anon";
GRANT ALL ON TABLE "public"."wallets" TO "authenticated";
GRANT ALL ON TABLE "public"."wallets" TO "service_role";



GRANT ALL ON TABLE "public"."admin_users_view" TO "anon";
GRANT ALL ON TABLE "public"."admin_users_view" TO "authenticated";
GRANT ALL ON TABLE "public"."admin_users_view" TO "service_role";



GRANT ALL ON TABLE "public"."bots" TO "anon";
GRANT ALL ON TABLE "public"."bots" TO "authenticated";
GRANT ALL ON TABLE "public"."bots" TO "service_role";



GRANT ALL ON TABLE "public"."chat_replies" TO "anon";
GRANT ALL ON TABLE "public"."chat_replies" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_replies" TO "service_role";



GRANT ALL ON TABLE "public"."chat_replies_with_user" TO "anon";
GRANT ALL ON TABLE "public"."chat_replies_with_user" TO "authenticated";
GRANT ALL ON TABLE "public"."chat_replies_with_user" TO "service_role";



GRANT ALL ON TABLE "public"."chats" TO "anon";
GRANT ALL ON TABLE "public"."chats" TO "authenticated";
GRANT ALL ON TABLE "public"."chats" TO "service_role";



GRANT ALL ON TABLE "public"."config_options" TO "anon";
GRANT ALL ON TABLE "public"."config_options" TO "authenticated";
GRANT ALL ON TABLE "public"."config_options" TO "service_role";



GRANT ALL ON TABLE "public"."free_generations" TO "anon";
GRANT ALL ON TABLE "public"."free_generations" TO "authenticated";
GRANT ALL ON TABLE "public"."free_generations" TO "service_role";



GRANT ALL ON TABLE "public"."generated_videos" TO "anon";
GRANT ALL ON TABLE "public"."generated_videos" TO "authenticated";
GRANT ALL ON TABLE "public"."generated_videos" TO "service_role";



GRANT ALL ON TABLE "public"."image_to_video_requests" TO "anon";
GRANT ALL ON TABLE "public"."image_to_video_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."image_to_video_requests" TO "service_role";



GRANT ALL ON TABLE "public"."images" TO "anon";
GRANT ALL ON TABLE "public"."images" TO "authenticated";
GRANT ALL ON TABLE "public"."images" TO "service_role";



GRANT ALL ON TABLE "public"."link_codes" TO "anon";
GRANT ALL ON TABLE "public"."link_codes" TO "authenticated";
GRANT ALL ON TABLE "public"."link_codes" TO "service_role";



GRANT ALL ON TABLE "public"."logo_generations" TO "anon";
GRANT ALL ON TABLE "public"."logo_generations" TO "authenticated";
GRANT ALL ON TABLE "public"."logo_generations" TO "service_role";



GRANT ALL ON TABLE "public"."payments" TO "anon";
GRANT ALL ON TABLE "public"."payments" TO "authenticated";
GRANT ALL ON TABLE "public"."payments" TO "service_role";



GRANT ALL ON TABLE "public"."token_packages" TO "anon";
GRANT ALL ON TABLE "public"."token_packages" TO "authenticated";
GRANT ALL ON TABLE "public"."token_packages" TO "service_role";



GRANT ALL ON TABLE "public"."token_transactions" TO "anon";
GRANT ALL ON TABLE "public"."token_transactions" TO "authenticated";
GRANT ALL ON TABLE "public"."token_transactions" TO "service_role";



GRANT ALL ON TABLE "public"."tools" TO "anon";
GRANT ALL ON TABLE "public"."tools" TO "authenticated";
GRANT ALL ON TABLE "public"."tools" TO "service_role";



GRANT ALL ON TABLE "public"."user_uploads" TO "anon";
GRANT ALL ON TABLE "public"."user_uploads" TO "authenticated";
GRANT ALL ON TABLE "public"."user_uploads" TO "service_role";



GRANT ALL ON TABLE "public"."web_generation_requests" TO "anon";
GRANT ALL ON TABLE "public"."web_generation_requests" TO "authenticated";
GRANT ALL ON TABLE "public"."web_generation_requests" TO "service_role";



GRANT ALL ON TABLE "public"."web_generation_steps" TO "anon";
GRANT ALL ON TABLE "public"."web_generation_steps" TO "authenticated";
GRANT ALL ON TABLE "public"."web_generation_steps" TO "service_role";



GRANT ALL ON TABLE "public"."web_image_edits" TO "anon";
GRANT ALL ON TABLE "public"."web_image_edits" TO "authenticated";
GRANT ALL ON TABLE "public"."web_image_edits" TO "service_role";



GRANT ALL ON TABLE "public"."web_images" TO "anon";
GRANT ALL ON TABLE "public"."web_images" TO "authenticated";
GRANT ALL ON TABLE "public"."web_images" TO "service_role";



GRANT ALL ON TABLE "public"."web_videos" TO "anon";
GRANT ALL ON TABLE "public"."web_videos" TO "authenticated";
GRANT ALL ON TABLE "public"."web_videos" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
