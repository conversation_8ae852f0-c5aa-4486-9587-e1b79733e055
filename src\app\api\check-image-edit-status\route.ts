import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function GET(request: NextRequest) {
  try {
    console.log('Checking image edit status...');

    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('id');

    if (!requestId) {
      return NextResponse.json({ 
        error: 'Request ID is required' 
      }, { status: 400 });
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json({ 
        error: 'Authentication required' 
      }, { status: 401 });
    }

    // Verify user authentication
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ 
        error: 'Invalid authentication' 
      }, { status: 401 });
    }

    // Get the request from database
    const { data: editRequest, error: fetchError } = await supabaseAdmin
      .from('web_image_edits')
      .select('*')
      .eq('id', requestId)
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('Error fetching image edit request:', fetchError);
      return NextResponse.json({ 
        error: 'Image edit request not found',
        details: fetchError.message
      }, { status: 404 });
    }

    if (!editRequest) {
      return NextResponse.json({ 
        error: 'Image edit request not found or access denied' 
      }, { status: 404 });
    }

    // Get the complete edit chain for this edit
    const { data: editChain, error: chainError } = await supabaseAdmin
      .rpc('get_edit_chain', { edit_id: requestId });

    if (chainError) {
      console.error('Error fetching edit chain:', chainError);
      // Don't fail the request, just return empty chain
    }

    // Get chain statistics
    const { data: chainStats, error: statsError } = await supabaseAdmin
      .rpc('get_edit_chain_stats', { edit_id: requestId });

    if (statsError) {
      console.error('Error fetching chain stats:', statsError);
      // Don't fail the request, just return null stats
    }

    // Return the current status with complete edit chain
    return NextResponse.json({
      request_id: editRequest.id,
      status: editRequest.status,
      original_image_url: editRequest.original_image_url,
      edited_image_url: editRequest.edited_image_url,
      prompt: editRequest.prompt,
      guidance_scale: editRequest.guidance_scale,
      safety_tolerance: editRequest.safety_tolerance,
      output_format: editRequest.output_format,
      seed: editRequest.seed,
      tokens_charged: editRequest.token_cost,
      is_public: editRequest.is_public,
      error_message: editRequest.error_message,
      created_at: editRequest.created_at,
      updated_at: editRequest.updated_at,
      completed_at: editRequest.completed_at,
      // Include complete edit chain
      edit_chain: editChain || [],
      chain_statistics: chainStats?.[0] || null
    });

  } catch (error: any) {
    console.error('Error checking image edit status:', error);
    return NextResponse.json(
      { 
        error: 'Failed to check status', 
        details: error.message 
      },
      { status: 500 }
    );
  }
} 