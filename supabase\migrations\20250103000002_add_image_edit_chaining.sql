-- The chaining fields already exist in the table, just need to add the functions
-- Create indexes for the chaining fields if they don't exist
CREATE INDEX IF NOT EXISTS idx_web_image_edits_parent_edit_id ON web_image_edits(parent_edit_id);
CREATE INDEX IF NOT EXISTS idx_web_image_edits_root_edit_id ON web_image_edits(root_edit_id);
CREATE INDEX IF NOT EXISTS idx_web_image_edits_edit_sequence ON web_image_edits(edit_sequence);

-- Function to get the complete edit chain for a given edit
CREATE OR REPLACE FUNCTION get_edit_chain(edit_id UUID)
RETURNS TABLE (
  id UUID,
  parent_edit_id UUID,
  root_edit_id UUID,
  edit_sequence INTEGER,
  original_image_url TEXT,
  edited_image_url TEXT,
  prompt TEXT,
  status VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  -- First, find the root edit ID for the given edit
  WITH root_finder AS (
    SELECT 
      COALESCE(we.root_edit_id, we.id) as root_id
    FROM web_image_edits we 
    WHERE we.id = edit_id
  )
  -- Return all edits in the chain, ordered by sequence
  RETURN QUERY
  SELECT 
    we.id,
    we.parent_edit_id,
    we.root_edit_id,
    we.edit_sequence,
    we.original_image_url,
    we.edited_image_url,
    we.prompt,
    we.status,
    we.created_at,
    we.completed_at
  FROM web_image_edits we
  CROSS JOIN root_finder rf
  WHERE we.root_edit_id = rf.root_id OR we.id = rf.root_id
  ORDER BY we.edit_sequence ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get edit chain statistics
CREATE OR REPLACE FUNCTION get_edit_chain_stats(edit_id UUID)
RETURNS TABLE (
  total_edits INTEGER,
  completed_edits INTEGER,
  failed_edits INTEGER,
  pending_edits INTEGER,
  processing_edits INTEGER,
  total_tokens INTEGER,
  chain_created_at TIMESTAMP WITH TIME ZONE,
  last_updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  -- First, find the root edit ID for the given edit
  WITH root_finder AS (
    SELECT 
      COALESCE(we.root_edit_id, we.id) as root_id
    FROM web_image_edits we 
    WHERE we.id = edit_id
  )
  -- Calculate statistics for the entire chain
  RETURN QUERY
  SELECT 
    COUNT(*)::INTEGER as total_edits,
    COUNT(CASE WHEN we.status = 'completed' THEN 1 END)::INTEGER as completed_edits,
    COUNT(CASE WHEN we.status = 'failed' THEN 1 END)::INTEGER as failed_edits,
    COUNT(CASE WHEN we.status = 'pending' THEN 1 END)::INTEGER as pending_edits,
    COUNT(CASE WHEN we.status = 'processing' THEN 1 END)::INTEGER as processing_edits,
    COALESCE(SUM(we.token_cost), 0)::INTEGER as total_tokens,
    MIN(we.created_at) as chain_created_at,
    MAX(we.updated_at) as last_updated_at
  FROM web_image_edits we
  CROSS JOIN root_finder rf
  WHERE we.root_edit_id = rf.root_id OR we.id = rf.root_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_edit_chain(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_edit_chain_stats(UUID) TO authenticated;

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION get_edit_chain(UUID) TO service_role;
GRANT EXECUTE ON FUNCTION get_edit_chain_stats(UUID) TO service_role;
