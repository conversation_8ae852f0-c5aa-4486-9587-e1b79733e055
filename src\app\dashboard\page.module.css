.mainContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: calc(100vh - 80px);
  }
  
  .footer {
    border-top: 1px solid #e5e7eb;
    padding: 1rem 0;
    margin-top: 2rem;
    background: #fff;
  }
  
  .footerContent {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .footerLinksSimple {
    display: flex;
    gap: 0.75rem;
    color: #666;
    font-size: 0.95rem;
  }
  
  .footerLinksSimple a {
    color: #4b5563;
    text-decoration: none;
  }
  
  .footerLinksSimple a:hover {
    text-decoration: underline;
  }
  
  .footerDivider {
    color: #9ca3af;
  }
  
  .startButton {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
  }
  
  /* Optional: styling for the two lines in the start button */
  .startButtonMain {
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  .startButtonSub {
    font-size: 0.85rem;
  }
  
  .chatsContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .chatCard {
    position: relative;
    flex: 0 0 calc(33% - 1rem);
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    cursor: pointer;
    transition: transform 0.1s ease-in-out;
  }
  
  .chatCard:hover {
    transform: scale(1.02);
  }
  
  .phaseBadge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #0070f3;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  
  .emptyBadge {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: red;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
  }
  
  .modalBackground {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modalContent {
    background: #fff;
    padding: 1rem;
    border-radius: 4px;
    max-width: 500px;
    width: 90%;
  }

  .imagesGrid {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: flex-start;
  }
  
  .imageCard {
    position: relative;
    background: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    padding: 1.25rem;
    width: 360px;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: box-shadow 0.15s, transform 0.15s;
  }
  
  .imageCard:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.13);
    transform: translateY(-2px) scale(1.03);
  }
  
  .generatedImage {
    width: 320px;
    height: 320px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: #f3f3f3;
  }
  
  .imageDetails {
    width: 100%;
    text-align: center;
    font-size: 0.98rem;
    color: #222;
    margin-top: 0.25rem;
  }
  
  .imageModalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
  }
  
  .imageModal {
    position: relative;
    background: #fff;
    border-radius: 10px;
    padding: 2rem 2rem 1.5rem 2rem;
    max-width: 95vw;
    max-height: 90vh;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .modalImage {
    max-width: 70vw;
    max-height: 60vh;
    border-radius: 10px;
    margin-bottom: 1rem;
    background: #f3f3f3;
  }
  
  .modalDetails {
    width: 100%;
    text-align: center;
  }
  
  .closeModalButton {
    position: absolute;
    top: 10px;
    right: 16px;
    background: none;
    border: none;
    font-size: 2rem;
    color: #888;
    cursor: pointer;
    z-index: 10;
    transition: color 0.15s;
  }
  
  .closeModalButton:hover {
    color: #222;
  }

  /* Tokens Page Styles */
  .tokenBalance {
    background: #4f46e5;
    color: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    font-size: 1.2rem;
    margin: 2rem 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .section {
    margin: 3rem 0;
  }

  .section h2 {
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.5rem;
  }

  .tokenPackages {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
  }

  .tokenPackage {
    background: #fff;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .tokenPackage:hover {
    border-color: #4f46e5;
    box-shadow: 0 8px 24px rgba(79, 70, 229, 0.15);
    transform: translateY(-2px);
  }

  .packageHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }

  .packageHeader h3 {
    color: #333;
    margin: 0;
    font-size: 1.25rem;
  }

  .packagePrice {
    background: #4f46e5;
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    font-weight: bold;
    font-size: 1.1rem;
  }

  .packageDetails {
    margin: 1.5rem 0;
  }

  .tokenAmount {
    font-size: 1.3rem;
    font-weight: bold;
    color: #333;
    margin: 0.75rem 0;
  }

  .packageDescription {
    color: #666;
    margin: 0.75rem 0;
    line-height: 1.5;
  }

  .tokenValue {
    color: #059669;
    font-weight: 600;
    margin: 0.75rem 0;
    font-size: 0.9rem;
  }

  .purchaseButton {
    width: 100%;
    background: #4f46e5;
    color: white;
    border: none;
    padding: 1.25rem 1.5rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1.5rem;
  }

  .purchaseButton:hover:not(:disabled) {
    background: #3730a3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
  }

  .purchaseButton:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
  }

  .purchasing {
    background: #6b7280;
  }

  .paymentHistory {
    background: #f9fafb;
    border-radius: 12px;
    padding: 2rem;
  }

  .paymentItem {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: box-shadow 0.2s ease;
  }

  .paymentItem:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }

  .paymentItem:last-child {
    margin-bottom: 0;
  }

  .paymentDetails {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .paymentPackage {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .paymentPackage strong {
    color: #333;
    font-size: 1.1rem;
  }

  .paymentAmount {
    background: #f3f4f6;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 16px;
    font-weight: 600;
    font-size: 0.9rem;
  }

  .paymentMeta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
  }

  .paymentMeta span {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: #f9fafb;
  }

  .paymentStatus {
    font-weight: 600;
    text-transform: capitalize;
  }

  .paymentStatus.completed {
    background: #dcfce7 !important;
    color: #166534 !important;
  }

  .paymentStatus.pending {
    background: #fef3c7 !important;
    color: #92400e !important;
  }

  .paymentStatus.failed {
    background: #fee2e2 !important;
    color: #dc2626 !important;
  }

  .paymentStatus.processing {
    background: #dbeafe !important;
    color: #1e40af !important;
  }

  .paymentDate {
    color: #9ca3af !important;
    background: transparent !important;
  }

  .error {
    background: #fee2e2;
    color: #dc2626;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px solid #fecaca;
  }

  .loading {
    text-align: center;
    padding: 3rem;
    font-size: 1.2rem;
    color: #666;
  }

  .downloadButton {
    position: absolute;
    bottom: 16px;
    right: 16px;
    background: none;
    border: none;
    cursor: pointer;
    z-index: 2;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .dashboardFlexRow {
    display: flex;
    gap: 32px;
    align-items: stretch;
    margin-bottom: 40px;
  }

  .dashboardFlexCol {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
  }

  @media (max-width: 900px) {
    .dashboardFlexRow {
      flex-direction: column;
      gap: 20px;
    }
    .dashboardFlexCol {
      margin-bottom: 0;
    }
  }

  /* Compact Video Modal Styles */
  .compactModalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
  }

  .compactModalContent {
    background-color: #000;
    border-radius: 8px;
    padding: 0;
    max-width: min(60vw, 600px);
    max-height: min(60vh, 450px);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .compactModalCloseButton {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border: none;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.5rem;
    font-weight: bold;
    z-index: 20;
    transition: background-color 0.2s;
    line-height: 1;
  }

  .compactModalCloseButton:hover {
    background: rgba(0, 0, 0, 0.9);
  }

  .compactModalVideo {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: contain;
  }

  /* Responsive modal adjustments */
  @media (max-width: 768px) {
    .compactModalOverlay {
      padding: 0.5rem;
    }

    .compactModalContent {
      max-width: 90vw;
      max-height: 70vh;
    }

    .compactModalCloseButton {
      width: 1.75rem;
      height: 1.75rem;
      font-size: 1rem;
    }
  }