"use client";

import React, { useState, useRef, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../../store';
import { fetchUserProfile } from '../../store/userSlice';
import { Upload, Image as ImageIcon, Download, ArrowClockwise } from 'react-bootstrap-icons';
import styles from '../admin/admin.module.css';
import Header from '@/components/Header';
import { logger } from '@/utils/logger';
import { supabase } from '../../lib/supabaseClient';
import { useTranslation } from '@/hooks/useTranslation';



interface EditChainItem {
  id: string;
  parent_edit_id: string | null;
  root_edit_id: string | null;
  edit_sequence: number;
  original_image_url: string;
  edited_image_url: string | null;
  prompt: string;
  status: string;
  created_at: string;
  completed_at: string | null;
}

interface EditChainItem {
  id: string;
  parent_edit_id: string | null;
  root_edit_id: string | null;
  edit_sequence: number;
  original_image_url: string;
  edited_image_url: string | null;
  prompt: string;
  status: string;
  created_at: string;
  completed_at: string | null;
}



interface ImageEdit {
  request_id: string;
  status: string;
  original_image_url: string;
  edited_image_url?: string;
  prompt: string;
  guidance_scale: number;
  safety_tolerance: string;
  output_format: string;
  seed?: number;
  tokens_charged: number;
  is_public: boolean;
  error_message?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

const ImageEditorContent: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.user);
  const router = useRouter();
  const dispatch = useDispatch<AppDispatch>();
  const searchParams = useSearchParams();
  const requestId = searchParams?.get('id');
  const { t } = useTranslation('ro'); // Using Romanian translations

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageFile, setSelectedImageFile] = useState<File | null>(null);
  const [prompt, setPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [editChain, setEditChain] = useState<EditChainItem[]>([]);
  const [chainStats, setChainStats] = useState<any>(null);
  const [showContinueInput, setShowContinueInput] = useState(false);
  const [continuePrompt, setContinuePrompt] = useState('');

  // Function to translate status values
  const translateStatus = (status: string): string => {
    switch (status) {
      case 'pending':
        return t('image_editor_chain_status_pending');
      case 'processing':
        return t('image_editor_chain_status_processing');
      case 'completed':
        return t('image_editor_chain_status_completed');
      case 'failed':
        return t('image_editor_chain_status_failed');
      default:
        return status;
    }
  };

  const [currentEdit, setCurrentEdit] = useState<ImageEdit | null>(null);
  const [isPolling, setIsPolling] = useState(false);
  const [isLoadingExistingEdit, setIsLoadingExistingEdit] = useState(!!requestId); // Initialize based on requestId

  // Ref to track if we've already started loading this edit
  const loadingEditRef = useRef<string | null>(null);
  // Ref to track the current polling timeout so we can clear it
  const pollingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  


  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  
  // Auto-resize textarea function
  const adjustTextareaHeight = useCallback(() => {
    if (textareaRef.current) {
      // Reset height to auto to get the actual content height
      textareaRef.current.style.height = 'auto';
      // Get the scroll height (content height)
      const scrollHeight = textareaRef.current.scrollHeight;
      // Set the height to the scroll height, but respect max-height from CSS
      const maxHeight = 300; // matches CSS max-height
      const minHeight = 100; // matches CSS min-height
      const finalHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      textareaRef.current.style.height = `${finalHeight}px`;
    }
  }, []);

  const pollEditStatus = useCallback(async (id: string) => {
    // Simple check: if already polling, don't start another
    if (isPolling) {
      console.log('Already polling, skipping...');
      return;
    }

    setIsPolling(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) return;

      const response = await fetch(`/api/check-image-edit-status?id=${id}`, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('Poll result:', result); // Debug log
        setCurrentEdit(result);
        setIsLoadingExistingEdit(false); // Clear loading state once we have data

        // Update edit chain and stats from the status response
        if (result.edit_chain) {
          setEditChain(result.edit_chain);
        }
        if (result.chain_statistics) {
          setChainStats(result.chain_statistics);
        }

        if (result.status === 'processing' || result.status === 'pending') {
          // Continue polling every 3 seconds
          pollingTimeoutRef.current = setTimeout(() => {
            setIsPolling(false); // Reset polling state before next poll
            pollEditStatus(id);
          }, 3000);
        } else {
          setIsPolling(false);
          setIsGenerating(false); // Stop showing loading state
        }
      } else {
        console.error('Failed to check edit status');
        setIsPolling(false);
        setIsLoadingExistingEdit(false);
      }
    } catch (error) {
      console.error('Error polling edit status:', error);
      setIsPolling(false);
      setIsLoadingExistingEdit(false);
    }
  }, [dispatch, isPolling]);

  // Poll for edit status if we have a request ID
  useEffect(() => {
    if (requestId && user) {
      // If this is a new requestId, reset the current edit state
      if (loadingEditRef.current !== requestId) {
        setCurrentEdit(null);
        setEditChain([]);
        setChainStats(null);
        setIsLoadingExistingEdit(true);
        loadingEditRef.current = requestId;
        // Start polling for the new edit
        pollEditStatus(requestId);
      }
    } else {
      // Clear loading state if no requestId (new edit)
      setIsLoadingExistingEdit(false);
      loadingEditRef.current = null;
    }

    // Cleanup function to clear any pending polling timeouts
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
        pollingTimeoutRef.current = null;
      }
    };
  }, [requestId, user, pollEditStatus]);

  // Refresh user balance when edit completes
  useEffect(() => {
    if (currentEdit?.status === 'completed') {
      // Refresh user profile to update credits/balance
      dispatch(fetchUserProfile());
    }
  }, [currentEdit?.status, dispatch]);

  // Auto-resize textarea when prompt changes
  useEffect(() => {
    adjustTextareaHeight();
  }, [prompt, adjustTextareaHeight]);

  // Auto-resize textarea on mount
  useEffect(() => {
    adjustTextareaHeight();
  }, [adjustTextareaHeight]);

  const handleFileSelect = (file: File) => {
    if (!file.type.startsWith('image/')) {
      setError(t('image_editor_error_file_type'));
      setSuccessMessage(null);
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setSelectedImage(result);
      setSelectedImageFile(file);
      setCurrentEdit(null);
      setError(null);
      setSuccessMessage(null);
    };
    reader.readAsDataURL(file);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handlePaste = useCallback(async (e: ClipboardEvent) => {
    e.preventDefault();
    
    const items = e.clipboardData?.items;
    if (!items) return;

    let foundImage = false;
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (file) {
          foundImage = true;
          handleFileSelect(file);
          setError(null);
          setSuccessMessage(t('image_editor_success_paste'));
          setTimeout(() => setSuccessMessage(null), 3000);
          break;
        }
      }
    }

    if (!foundImage && items.length > 0) {
      setError(t('image_editor_error_clipboard'));
    }
  }, [t]);

  // Set up paste event listener
  useEffect(() => {
    const handleGlobalPaste = (e: ClipboardEvent) => {
      const activeElement = document.activeElement;
      if (activeElement && (
        activeElement.tagName === 'INPUT' || 
        activeElement.tagName === 'TEXTAREA'
      )) {
        return;
      }
      handlePaste(e);
    };

    document.addEventListener('paste', handleGlobalPaste);
    return () => document.removeEventListener('paste', handleGlobalPaste);
  }, [handlePaste]);

  const uploadImageToStorage = async (file: File): Promise<string> => {
    if (!user) {
      throw new Error('User must be authenticated to upload images');
    }

    try {
      // Generate unique filename
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(2, 15);
      const extension = file.name.split('.').pop() || 'jpg';
      const fileName = `image-edits/${user.id}/${timestamp}-${randomId}.${extension}`;

      // Upload file to Supabase Storage
      const { error } = await supabase.storage
        .from('generated-images')
        .upload(fileName, file, {
          contentType: file.type,
          upsert: false
        });

      if (error) {
        console.error('Storage upload error:', error);
        throw new Error(`Failed to upload image: ${error.message}`);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('generated-images')
        .getPublicUrl(fileName);

      if (!urlData?.publicUrl) {
        throw new Error('Failed to get public URL for uploaded image');
      }

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image to storage:', error);
      throw error;
    }
  };

  const handleEditImage = async () => {
    if (!selectedImageFile || !prompt.trim()) {
      setError(t('image_editor_error_no_image_prompt'));
      return;
    }

    if (!user) {
      setError(t('image_editor_error_sign_in'));
      return;
    }

    // Scroll to top of page to show loading state and results
    window.scrollTo({ top: 0, behavior: 'smooth' });

    setIsGenerating(true);
    setError(null);
    setCurrentEdit(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error(t('image_editor_error_auth_required'));
      }

      // Upload image to storage to get URL that FAL can access
      const imageDataUrl = await uploadImageToStorage(selectedImageFile);
      
      const response = await fetch('/api/image-edit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          prompt: prompt.trim(),
          image_url: imageDataUrl,
          guidance_scale: 3.5,
          safety_tolerance: "2",
          output_format: 'jpeg'
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || t('image_editor_error_edit_image'));
      }

      if (result.success && result.request_id) {
        // Redirect to URL with request ID for persistence
        router.push(`/image-editor?id=${result.request_id}`);
      } else {
        throw new Error(t('image_editor_error_no_request_id'));
      }

    } catch (error: any) {
      logger.error('Error editing image:', error);
      setError(error.message || t('image_editor_error_edit_image'));
      setIsGenerating(false);
    }
  };

  const handleDownload = (imageUrl: string, filename: string) => {
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const resetEditor = () => {
    setSelectedImage(null);
    setSelectedImageFile(null);
    setCurrentEdit(null);
    setPrompt('');
    setError(null);
    setSuccessMessage(null);

    setEditChain([]);
    setChainStats(null);

    setIsPolling(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    // Clear URL params
    router.push('/image-editor');
  };

  const handleConvertToVideo = (imageUrl: string) => {
    // Navigate to image-to-video page with the image URL
    router.push(`/image-to-video?imageUrl=${encodeURIComponent(imageUrl)}`);
  };



  // Function to continue editing from a completed edit
  const continueEditing = async (parentEditId: string, newPrompt: string) => {
    if (!newPrompt.trim()) {
      setError('Please enter a prompt for the next edit');
      return;
    }

    setIsGenerating(true);
    setError(null);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Authentication required');
      }

      const response = await fetch('/api/image-edit-chain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          parent_edit_id: parentEditId,
          prompt: newPrompt.trim(),
          guidance_scale: 3.5,
          safety_tolerance: "2",
          output_format: 'jpeg'
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to start chained edit');
      }

      if (result.success && result.request_id) {
        // Reset input state before redirecting
        setShowContinueInput(false);
        setContinuePrompt('');
        // Redirect to the new chained edit
        router.push(`/image-editor?id=${result.request_id}`);
      } else {
        throw new Error('No request ID returned');
      }

    } catch (error: any) {
      logger.error('Error creating chained edit:', error);
      setError(error.message || 'Failed to start chained edit');
      setIsGenerating(false);
      // Reset input state on error
      setShowContinueInput(false);
      setContinuePrompt('');
    }
  };

  const renderStatus = () => {
    if (!currentEdit) return null;

    switch (currentEdit.status) {
      case 'pending':
        return (
          <div className={styles.statusMessage}>
            <ArrowClockwise size={16} className={styles.spinning} />
            <span>{t('image_editor_status_pending')}</span>
          </div>
        );
      case 'processing':
        return (
          <div className={styles.statusMessage}>
            <ArrowClockwise size={16} className={styles.spinning} />
            <span>{t('image_editor_status_processing')}</span>
          </div>
        );
      case 'failed':
        return (
          <div className={styles.errorMessage}>
            <strong>{t('image_editor_status_failed')}</strong> {currentEdit.error_message || 'Unknown error occurred'}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Header />
      <div className={styles.pageContainer}>
        <div className={styles.pageHeader}>
          <h2>{t('image_editor_title')}</h2>
          <p>{currentEdit?.status === 'completed' ? t('image_editor_results_completed') : t('image_editor_subtitle')}</p>
        </div>

        <div className={styles.imageEditorContainer}>
          {/* Loading state for existing edits */}
          {isLoadingExistingEdit && (
            <div className={styles.loading}>
              <ArrowClockwise size={32} className={styles.spinning} />
              <p>{t('image_editor_loading_edit')}</p>
            </div>
          )}

          {/* Status Display */}
          {!isLoadingExistingEdit && renderStatus()}

          {/* Upload Section - Hide when there are results to show or when loading */}
          {!isLoadingExistingEdit && !currentEdit?.edited_image_url && (
          <div className={styles.uploadSection}>
            <div
              ref={dropZoneRef}
              className={`${styles.dropZone} ${selectedImage || currentEdit?.original_image_url ? styles.hasImage : ''}`}
              onDrop={currentEdit ? undefined : handleDrop}
              onDragOver={currentEdit ? undefined : handleDragOver}
              onClick={currentEdit ? undefined : () => fileInputRef.current?.click()}
            >
              {selectedImage || currentEdit?.original_image_url ? (
                <div className={styles.selectedImageContainer}>
                  <img
                    src={selectedImage || currentEdit?.original_image_url}
                    alt="Selected"
                    className={styles.selectedImage}
                  />
                  {!currentEdit && (
                    <div className={styles.imageOverlay}>
                      <Upload size={24} />
                      <span>{t('image_editor_click_to_replace')}</span>
                    </div>
                  )}
                  {(currentEdit?.status === 'pending' || currentEdit?.status === 'processing') && (
                    <div className={styles.imageOverlayProcessing}>
                      <ArrowClockwise size={32} className={styles.spinning} />
                    </div>
                  )}
                </div>
              ) : (
                <div className={styles.uploadPrompt}>
                  <ImageIcon size={48} />
                  <h3>{t('image_editor_upload_title')}</h3>
                  <p>{t('image_editor_upload_description')}</p>
                  <p className={styles.supportedFormats}>{t('image_editor_supported_formats')}</p>
                  <p className={styles.pasteHint}>{t('image_editor_paste_tip')}</p>
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
              style={{ display: 'none' }}
            />
          </div>
          )}

          {/* Edit Controls - Only show input when no edit is in progress and no results exist */}
          {selectedImage && !currentEdit && (
            <div className={styles.editControls}>
              {/* Insufficient Credits Warning for Initial Edit */}
              {user && user.tokens < 10 && (
                <div className={styles.warningMessage}>
                  <strong>⚠️ {t('image_editor_insufficient_credits_warning')}</strong>
                  <p>{t('image_editor_insufficient_credits_message', { required: 10, available: user.tokens })}</p>
                </div>
              )}

              <div className={styles.promptSection}>
                <label htmlFor="prompt">{t('image_editor_prompt_label')}</label>
                <textarea
                  id="prompt"
                  ref={textareaRef}
                  value={prompt}
                  onChange={(e) => {
                    setPrompt(e.target.value);
                    adjustTextareaHeight();
                  }}
                  placeholder={t('image_editor_prompt_placeholder')}
                  className={styles.promptInput}
                  disabled={!!currentEdit || (user?.tokens || 0) < 10}
                />
              </div>

              <div className={styles.controlButtons}>
                <button
                  onClick={handleEditImage}
                  disabled={isGenerating || !prompt.trim() || !!currentEdit || (user?.tokens || 0) < 10}
                  className={styles.editButton}
                >
                  {isGenerating || isPolling ? (
                    <>
                      <ArrowClockwise size={16} className={styles.spinning} />
                      {isGenerating ? t('image_editor_submitting') : t('image_editor_processing')}
                    </>
                  ) : (
                    t('image_editor_edit_button')
                  )}
                </button>
              </div>


            </div>
          )}



          {/* Error Display - Hide when loading existing edit */}
          {!isLoadingExistingEdit && error && (
            <div className={styles.errorMessage}>
              <strong>{t('image_editor_error')}</strong> {error}
            </div>
          )}

          {/* Success Display - Hide when loading existing edit */}
          {!isLoadingExistingEdit && successMessage && (
            <div className={styles.successMessage}>
              <strong>{t('image_editor_success')}</strong> {successMessage}
            </div>
          )}

          {/* Results Section - Hide when loading existing edit */}
          {!isLoadingExistingEdit && ((selectedImage || currentEdit?.original_image_url) && currentEdit?.edited_image_url) && (
            <div className={styles.resultsSection}>
              <div className={styles.imageComparison}>
                {currentEdit?.edited_image_url && (
                  <div className={styles.imagePanel}>
                    <h4>{t('image_editor_edited')}</h4>
                    <img src={currentEdit.edited_image_url} alt="Edited" className={styles.resultImage} />

               
                    
                  </div>
                )}

                {(selectedImage || currentEdit?.original_image_url) && (
                  <div className={styles.imagePanel}>
                    <h4>{t('image_editor_original')}</h4>
                    <div style={{ position: 'relative', display: 'inline-block', width: 'fit-content' }}>
                      <img
                        src={selectedImage || currentEdit?.original_image_url}
                        alt="Original"
                        className={styles.resultImage}
                      />
                      {(currentEdit?.status === 'pending' || currentEdit?.status === 'processing') && (
                        <div className={styles.imageOverlayProcessing}>
                          <ArrowClockwise size={32} className={styles.spinning} />
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>


              {/* Action Buttons Section */}
              {currentEdit?.edited_image_url && (
                <div className={styles.actionsSection}>
                  <button
                    onClick={() => handleDownload(currentEdit.edited_image_url!, `edited-${currentEdit.request_id}.${currentEdit.output_format}`)}
                    className={styles.downloadButton}
                  >
                    <Download size={16} />
                    {t('image_editor_download')}
                  </button>

                  <button
                    onClick={() => setShowContinueInput(!showContinueInput)}
                    className={styles.continueButton}
                    disabled={isGenerating}
                  >
                    <ArrowClockwise size={16} />
                    {t('image_editor_continue_editing')}
                  </button>

                  {currentEdit?.status === 'completed' && (
                    <button
                      onClick={() => handleConvertToVideo(currentEdit.edited_image_url!)}
                      className={styles.convertToVideoButton}
                    >
                      🎬 {t('image_edit_convert_to_video')}
                    </button>
                  )}
                </div>
              )}

              {/* Continue Editing Input */}
              {showContinueInput && currentEdit?.status === 'completed' && (
                <div className={styles.continueEditingSection}>
                  {/* Low Credits Warning */}
                  {user && user.tokens < 10 && (
                    <div className={styles.warningMessage}>
                      <strong>⚠️ {t('image_editor_insufficient_credits_warning')}</strong>
                      <p>{t('image_editor_insufficient_credits_message', { required: 10, available: user.tokens })}</p>
                    </div>
                  )}

                  <div className={styles.promptSection}>
                    <label htmlFor="continue-prompt">{t('image_editor_continue_prompt')}</label>
                    <textarea
                      id="continue-prompt"
                      value={continuePrompt}
                      onChange={(e) => setContinuePrompt(e.target.value)}
                      placeholder={t('image_editor_prompt_placeholder')}
                      className={styles.promptInput}
                      rows={3}
                      disabled={(user?.tokens || 0) < 10}
                    />
                  </div>
                  <div className={styles.continueActions}>
                    <button
                      onClick={() => {
                        if (continuePrompt.trim()) {
                          // Scroll to top of page to show loading state and results
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                          continueEditing(currentEdit.request_id, continuePrompt.trim());
                          // Don't reset the input state immediately - let the continueEditing function handle it
                        }
                      }}
                      disabled={!continuePrompt.trim() || isGenerating || (user?.tokens || 0) < 10}
                      className={styles.editButton}
                    >
                      {isGenerating ? (
                        <>
                          <ArrowClockwise size={16} className={styles.spinning} />
                          {t('image_editor_processing')}
                        </>
                      ) : (
                        <>
                          <ArrowClockwise size={16} />
                          {t('image_editor_continue_editing')}
                        </>
                      )}
                    </button>
                    <button
                      onClick={() => {
                        setShowContinueInput(false);
                        setContinuePrompt('');
                      }}
                      className={styles.resetButton}
                    >
                      {t('image_editor_cancel')}
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}



          {/* Edit Chain */}
          {editChain.length > 1 && (
            <div className={styles.chainSection}>
              <div className={styles.chainHeader}>
                <h3>{t('image_editor_edit_chain')}</h3>
                {chainStats && (
                  <div className={styles.chainStats}>
                    <span>{t('image_editor_total_edits')} {chainStats.total_edits}</span>
                  </div>
                )}
              </div>

              <div className={styles.chainFlow}>
                  {/* Show original image as step 0 */}
                  {editChain.length > 0 && editChain[0].original_image_url && (
                    <div className={styles.chainItem}>
                      <div className={styles.chainStep}>
                        <div className={styles.stepNumber}>0</div>
                        <div className={styles.stepContent}>
                          <div className={styles.stepImages}>
                            <img
                              src={editChain[0].original_image_url}
                              alt="Original uploaded image"
                              className={styles.resultImage}
                            />
                          </div>
                          <div className={styles.stepDetails}>
                            <p><strong>{t('image_editor_original_image')}</strong></p>
                            <p><small>{t('image_editor_uploaded')} {new Date(editChain[0].created_at).toLocaleString()}</small></p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Show all edited versions */}
                  {editChain.map((edit) => (
                    <div key={edit.id} className={styles.chainItem}>
                      <div className={styles.chainStep}>
                        <div className={styles.stepNumber}>{edit.edit_sequence}</div>
                        <div className={styles.stepContent}>
                          <div className={styles.stepImages}>
                            {edit.edited_image_url && (
                              <img
                                src={edit.edited_image_url}
                                alt={`Step ${edit.edit_sequence} result`}
                                className={styles.resultImage}
                              />
                            )}
                          </div>
                          <div className={styles.stepDetails}>
                            <p><strong>{t('image_editor_chain_prompt')}</strong> {edit.prompt}</p>
                            <p><small>{t('image_editor_chain_status')} {translateStatus(edit.status)}</small></p>
                            {edit.completed_at && (
                              <p><small>{t('image_editor_chain_completed')} {new Date(edit.completed_at).toLocaleString()}</small></p>
                            )}
                          </div>
                          {edit.status === 'completed' && edit.edited_image_url && (
                            <div className={styles.stepActions}>
                              <button
                                onClick={() => handleDownload(edit.edited_image_url!, `edit-${edit.edit_sequence}-${edit.id}.jpeg`)}
                                className={styles.downloadButton}
                              >
                                <Download size={16} />
                                {t('image_editor_download')}
                              </button>
                              <button
                                onClick={() => handleConvertToVideo(edit.edited_image_url!)}
                                className={styles.convertToVideoButton}
                              >
                                🎬 {t('image_edit_convert_to_video')}
                              </button>
                            </div>
                          )}
                        </div>
                      </div>

                    </div>
                  ))}
                </div>
            </div>
          )}
        </div>

        {/* Start New Edit Button - Show when there are results OR when edit failed */}
        {(currentEdit?.edited_image_url || currentEdit?.status === 'failed') && (
          <div className={styles.newEditSection}>
            <button
              onClick={resetEditor}
              className={styles.newEditButton}
            >
              {t('image_editor_start_new_edit')}
            </button>
          </div>
        )}
      </div>

      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerLinksSimple}>
            <a href="/privacy">Politica de Confidențialitate</a>
            <span className={styles.footerDivider}>•</span>
            <a href="/terms">Termeni și Condiții</a>
          </div>
        </div>
      </footer>
    </>
  );
};

const ImageEditor: React.FC = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ImageEditorContent />
    </Suspense>
  );
};

export default ImageEditor; 