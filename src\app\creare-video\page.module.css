.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
}

.logo {
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.logo:hover {
  opacity: 0.8;
}

.nav {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.navLink {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: color 0.2s;
}

.navLink:hover {
  color: #0070f3;
}

.navButton {
  padding: 0.5rem 1rem;
  background-color: transparent;
  color: #333;
  border: 1px solid #333;
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.navButton:hover {
  background-color: #333;
  color: white;
}

.errorText {
  color: red;
  font-weight: 500;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 200;
}

.hamburgerLine {
  display: block;
  width: 100%;
  height: 3px;
  background-color: #333;
  border-radius: 3px;
  transition: all 0.3s ease;
}

.hamburger.active .hamburgerLine:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.hamburger.active .hamburgerLine:nth-child(2) {
  opacity: 0;
}

.hamburger.active .hamburgerLine:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

.hero {
  text-align: center;
  padding: 3rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  color: white;
  max-width: 1200px;
  margin: 2rem auto 4rem auto;
  width: 100%;
}

@media (max-width: 1240px) {
  .hero {
    margin: 2rem 1rem 4rem 1rem;
  }
}

.title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 1.3rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 3rem;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2.5rem;
  color: #333;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.featureIcon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature h3 {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.feature p {
  color: #666;
  line-height: 1.6;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  padding: 2rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.stepNumber {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  flex-shrink: 0;
}

.stepContent h3 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.stepContent p {
  color: #666;
  line-height: 1.6;
}

.examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 900px;
  margin: 0 auto;
}

.example {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border-left: 5px solid #667eea;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.example:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.15);
}

.example h4 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.example p {
  color: #666;
  line-height: 1.5;
  font-size: 0.95rem;
}

.pricing {
  text-align: center;
  background: #f8f9fa;
  padding: 3rem 2rem;
  border-radius: 20px;
}

.pricingCard {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  max-width: 400px;
  margin: 0 auto;
}

.pricingCard h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #333;
}

.price {
  font-size: 3rem;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.priceNote {
  color: #666;
  margin-bottom: 2rem;
  font-style: italic;
}

.pricingFeatures {
  list-style: none;
  padding: 0;
  text-align: left;
}

.pricingFeatures li {
  padding: 0.5rem 0;
  color: #666;
  position: relative;
  padding-left: 1.5rem;
}

.pricingFeatures li::before {
  content: '✓';
  color: #28a745;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.cta {
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 20px;
}

.cta h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.cta p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.ctaButton {
  display: inline-block;
  background: white;
  color: #667eea;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.ctaButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.3);
  color: #5a67d8;
}

/* Footer */
.footer {
  background-color: #1a1a1a;
  color: white;
  padding: 4rem 2rem;
  margin-top: 4rem;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 3fr;
  gap: 4rem;
}

.footerLogo p {
  margin-top: 1rem;
  color: #a0a0a0;
}

.footerLinks {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.footerLinkColumn h4 {
  margin-bottom: 1rem;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
}

.footerLinkColumn a {
  display: block;
  color: #a0a0a0;
  text-decoration: none;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

.footerLinkColumn a:hover {
  color: white;
}

@media (max-width: 768px) {
  .hamburger {
    display: flex;
  }

  .nav {
    position: fixed;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100vh;
    background-color: white;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    transition: left 0.3s ease;
    z-index: 100;
  }

  .nav.menuOpen {
    left: 0;
  }

  .navLink {
    font-size: 1.25rem;
  }

  .navButton {
    font-size: 1.25rem;
  }

  .title {
    font-size: 2.5rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
  }
  
  .sectionTitle {
    font-size: 2rem;
  }
  
  .step {
    flex-direction: column;
    text-align: center;
  }
  
  .features {
    grid-template-columns: 1fr;
  }
  
  .examples {
    grid-template-columns: 1fr;
  }
}
