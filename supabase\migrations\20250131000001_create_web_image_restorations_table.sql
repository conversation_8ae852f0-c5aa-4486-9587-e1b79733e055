-- Create web_image_restorations table
CREATE TABLE web_image_restorations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE,
  
  -- Request details
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  original_image_url TEXT NOT NULL,
  
  -- Settings (simplified for restoration - no prompt needed)
  output_format VARCHAR(10) DEFAULT 'jpeg' CHECK (output_format IN ('jpeg', 'png')),
  seed INTEGER,
  
  -- Generated content
  restored_image_url TEXT,
  fal_request_id TEXT,
  
  -- Cost tracking
  token_cost INTEGER DEFAULT 10,
  
  -- Public visibility
  is_public BOOLEAN DEFAULT FALSE,
  
  -- Error handling
  error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- <PERSON><PERSON> indexes for performance
CREATE INDEX idx_web_image_restorations_user_id ON web_image_restorations(user_id);
CREATE INDEX idx_web_image_restorations_status ON web_image_restorations(status);
CREATE INDEX idx_web_image_restorations_created_at ON web_image_restorations(created_at);
CREATE INDEX idx_web_image_restorations_is_public ON web_image_restorations(is_public);

-- Create updated_at trigger
CREATE TRIGGER update_web_image_restorations_updated_at
  BEFORE UPDATE ON web_image_restorations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE web_image_restorations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own image restorations"
  ON web_image_restorations
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own image restorations"
  ON web_image_restorations
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own image restorations"
  ON web_image_restorations
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Public restorations can be viewed by anyone
CREATE POLICY "Anyone can view public image restorations"
  ON web_image_restorations
  FOR SELECT
  USING (is_public = TRUE);

-- Admin policies
CREATE POLICY "Admins can view all image restorations"
  ON web_image_restorations
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.user_metadata->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can update all image restorations"
  ON web_image_restorations
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.user_metadata->>'role' = 'admin'
    )
  );

-- Service role can do everything (for API operations)
CREATE POLICY "Service role can manage image restorations"
  ON web_image_restorations
  FOR ALL
  USING (current_setting('role') = 'service_role');
