import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabaseClient';

export async function GET(request: NextRequest) {
  try {
    // Fetch public image edits from web_image_edits table
    const { data: imageEdits, error } = await supabase
      .from('web_image_edits')
      .select(`
        id,
        original_image_url,
        edited_image_url,
        prompt,
        created_at,
        is_public
      `)
      .eq('is_public', true)
      .not('edited_image_url', 'is', null)
      .order('created_at', { ascending: false })
      .limit(3);

    if (error) {
      console.error('Error fetching public image edits:', error);
      return NextResponse.json({ error: 'Failed to fetch public image edits' }, { status: 500 });
    }

    return NextResponse.json({ imageEdits: imageEdits || [] });

  } catch (error) {
    console.error('Error in public image edits API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 