"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '../../../lib/supabaseClient';
import Header from '@/components/Header';
import Button from 'react-bootstrap/Button';
import ImageEdit from '../ImageEdit';
import { useTranslation } from '@/hooks/useTranslation';
import styles from './page.module.css';

export default function ImageEditsPage() {
  const router = useRouter();
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [imageEdits, setImageEdits] = useState<any[]>([]);
  const [imageEditsLoading, setImageEditsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // Always use Romanian translations
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  const ITEMS_PER_PAGE = 10;

  useEffect(() => {
    async function checkSession() {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/login');
      } else {
        setSession(session);
        setLoading(false);
      }
    }
    checkSession();
  }, [router]);

  useEffect(() => {
    async function fetchImageEdits() {
      if (!session?.user?.id) {
        setImageEditsLoading(false);
        return;
      }

      setImageEditsLoading(true);

      // First get the total count
      const { count, error: countError } = await supabase
        .from('web_image_edits')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', session.user.id);

      if (!countError && count !== null) {
        setTotalCount(count);
        setHasMore(count > currentPage * ITEMS_PER_PAGE);
      }

      // Load image edit data including image URLs for display
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;
      const { data, error } = await supabase
        .from('web_image_edits')
        .select(`
          id,
          status,
          prompt,
          token_cost,
          created_at,
          completed_at,
          error_message,
          original_image_url,
          edited_image_url
        `)
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .range(offset, offset + ITEMS_PER_PAGE - 1);

      if (!error && data) {
        setImageEdits(data);
      } else {
        console.error('Error fetching image edits:', error);
      }
      setImageEditsLoading(false);
    }
    fetchImageEdits();
  }, [session, currentPage, ITEMS_PER_PAGE]);

  const handleViewDetails = (id: string) => {
    // Navigate to image editor with the specific edit ID
    router.push(`/image-editor?edit=${id}`);
  };

  const handleConvertToVideo = (imageUrl: string) => {
    // Navigate to image-to-video page with the image URL
    router.push(`/image-to-video?imageUrl=${encodeURIComponent(imageUrl)}`);
  };

  return (
    <>
      <Header />
      <main className={styles.mainContainer}>
        {loading ? (
          <div>Loading...</div>
        ) : (
          <>
            <div className={styles.headerSection}>
              <div className={styles.titleContainer}>
                <h1 className={styles.pageTitle}>{t('image_edits_page_title')}</h1>
                {!imageEditsLoading && (
                  <span className={styles.counter}>
                    {t('image_edits_counter', { count: imageEdits.length })}
                  </span>
                )}
              </div>
              <Button variant="outline-secondary" onClick={() => router.push('/dashboard')}>
                {t('image_edits_back_to_dashboard')}
              </Button>
            </div>

            <div className={styles.subtitleSection}>
              <p className={styles.subtitle}>
                {t('image_edits_page_subtitle')}
              </p>
            </div>

            {imageEditsLoading ? (
              <div className={styles.loadingContainer}>
                <div className={styles.spinner}></div>
                {t('image_edits_loading')}
              </div>
            ) : imageEdits.length === 0 ? (
              <div className={styles.emptyState}>
                <div className={styles.emptyStateIcon}>🎨</div>
                <div className={styles.emptyStateTitle}>{t('image_edits_no_edits_title')}</div>
                <div>{t('image_edits_no_edits_description')}</div>
                <Button
                  variant="primary"
                  className={styles.startEditingButton}
                  onClick={() => router.push('/image-editor')}
                >
                  {t('image_edits_start_editing')}
                </Button>
              </div>
            ) : (
              <>
                <div className={styles.editsContainer}>
                  {imageEdits.map(edit => (
                    <ImageEdit
                      key={edit.id}
                      edit={edit}
                      t={t}
                      onViewDetails={handleViewDetails}
                      onConvertToVideo={handleConvertToVideo}
                    />
                  ))}
                </div>

                {/* Pagination Controls */}
                {totalCount > ITEMS_PER_PAGE && (
                  <div className={styles.paginationContainer}>
                    <Button
                      variant="outline-secondary"
                      disabled={currentPage === 1}
                      onClick={() => setCurrentPage(currentPage - 1)}
                    >
                      {t('pagination_previous')}
                    </Button>

                    <span className={styles.paginationInfo}>
                      {t('pagination_info', {
                        current: currentPage,
                        total: Math.ceil(totalCount / ITEMS_PER_PAGE),
                        showing: imageEdits.length,
                        totalItems: totalCount
                      })}
                    </span>

                    <Button
                      variant="outline-secondary"
                      disabled={!hasMore}
                      onClick={() => setCurrentPage(currentPage + 1)}
                    >
                      {t('pagination_next')}
                    </Button>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </main>
    </>
  );
}
