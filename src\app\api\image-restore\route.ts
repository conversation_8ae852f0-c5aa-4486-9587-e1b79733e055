import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { processImageRestorationAsync } from '@/utils/imageRestorationProcessor';

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    
    const { 
      image_url, 
      token_cost = 10,
      output_format = "jpeg",
      seed 
    } = await request.json();

    // Validate required fields - only image_url is required for restoration
    if (!image_url) {
      return NextResponse.json(
        { error: 'image_url is required' },
        { status: 400 }
      );
    }

    // Check if FAL API key is configured
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Verify the user's session
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);
    if (userError || !user) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('id, tokens')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Check if user has enough tokens
    if (wallet.tokens < token_cost) {
      return NextResponse.json(
        { error: 'Insufficient tokens', required: token_cost, available: wallet.tokens },
        { status: 400 }
      );
    }

    // Deduct tokens from wallet
    const { error: deductError } = await supabaseAdmin
      .from('wallets')
      .update({ 
        tokens: wallet.tokens - token_cost,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet.id);

    if (deductError) {
      console.error('Error deducting tokens:', deductError);
      return NextResponse.json(
        { error: 'Failed to deduct tokens' },
        { status: 500 }
      );
    }

    // Create record in web_image_restorations table
    const { data: restorationRecord, error: insertError } = await supabaseAdmin
      .from('web_image_restorations')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        status: 'pending',
        original_image_url: image_url,
        output_format: output_format,
        seed: seed,
        token_cost: token_cost,
        is_public: false
      })
      .select()
      .single();

    if (insertError || !restorationRecord) {
      console.error('Error creating restoration record:', insertError);
      
      // Refund tokens on failure
      await supabaseAdmin
        .from('wallets')
        .update({ 
          tokens: wallet.tokens,
          updated_at: new Date().toISOString()
        })
        .eq('id', wallet.id);

      return NextResponse.json(
        { error: 'Failed to create restoration record' },
        { status: 500 }
      );
    }

    // Start async processing (don't await)
    processImageRestorationAsync(restorationRecord.id).catch(error => {
      console.error('Async restoration processing error:', error);
    });

    // Return the request ID immediately
    return NextResponse.json({
      success: true,
      request_id: restorationRecord.id,
      status: 'pending'
    });

  } catch (error: any) {
    console.error('Image restoration error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to start image restoration', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
}
