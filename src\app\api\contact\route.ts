import { NextResponse } from 'next/server';
import { logger } from '@/utils/logger';

export async function POST(request: Request) {
  try {
    const body = await request.json().catch(() => null);
    if (!body) {
      return NextResponse.json({ error: 'Invalid JSON body' }, { status: 400 });
    }

    const { name, email, message } = body as { name?: string; email?: string; message?: string };
    if (!name || !email || !message) {
      return NextResponse.json({ error: 'Lipsesc câmpuri obligatorii.' }, { status: 400 });
    }

    // Basic length validations
    if (name.length > 200 || email.length > 320 || message.length > 5000) {
      return NextResponse.json({ error: 'Dimensiune câmp depășită.' }, { status: 400 });
    }

    // Log the inquiry server-side; in production, this can be replaced with emailing or ticketing.
    logger.log('Contact form submission', { name, email, messagePreview: message.slice(0, 200) });

    return NextResponse.json({ ok: true });
  } catch (err: any) {
    logger.error('Contact form error', err?.message || err);
    return NextResponse.json({ error: 'Eroare internă.' }, { status: 500 });
  }
}




