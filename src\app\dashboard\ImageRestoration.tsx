import React from 'react';
import Button from 'react-bootstrap/Button';
import { format } from 'date-fns';
import styles from './ImageEdit.module.css'; // Reuse the same styles

interface ImageRestorationProps {
  restoration: any;
  t: (key: string) => string;
  onViewDetails: (id: string) => void;
  onConvertToVideo?: (imageUrl: string) => void;
}

const ImageRestoration: React.FC<ImageRestorationProps> = ({ restoration, t, onViewDetails, onConvertToVideo }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'processing': return '#ffc107';
      case 'pending': return '#ffc107';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Finalizat';
      case 'processing': return 'În Procesare';
      case 'pending': return 'În Procesare';
      case 'failed': return 'Eșuat';
      default: return status;
    }
  };

  return (
  <div className={styles.imageEditCard}>
    <div className={styles.imageEditLeft}>
      <div className={styles.imageEditImages}>
        {restoration.original_image_url && (
          <div className={styles.imageEditImageContainer}>
            <img src={restoration.original_image_url} alt="Original" className={styles.imageEditImage} />
            <div className={styles.imageEditImageLabel}>📷 Original</div>
          </div>
        )}
        {restoration.restored_image_url && (
          <div className={styles.imageEditImageContainer}>
            <img src={restoration.restored_image_url} alt="Restored" className={styles.imageEditImage} />
            <div className={styles.imageEditImageLabel}>✨ Restored</div>
          </div>
        )}
      </div>
      <div className={styles.imageEditInfoBlock}>
        <div className={styles.imageEditInfo}>
          <strong>🔧 Type</strong> <span>Automatic Restoration</span>
        </div>
        <div className={styles.imageEditInfo}>
          <strong>Status</strong> <span style={{ color: getStatusColor(restoration.status), fontWeight: 500 }}>{getStatusText(restoration.status)}</span>
        </div>
        <div className={styles.imageEditInfo}>
          <strong>Cost</strong> <span>{restoration.token_cost} tokens</span>
        </div>
        <div className={styles.imageEditInfo}>
          <strong>Created</strong> {restoration.created_at ? format(new Date(restoration.created_at), 'yyyy-MM-dd HH:mm') : '-'}
        </div>
        {restoration.completed_at && (
          <div className={styles.imageEditInfo}>
            <strong>Completed</strong> {format(new Date(restoration.completed_at), 'yyyy-MM-dd HH:mm')}
          </div>
        )}
      </div>
    </div>
    <div className={styles.imageEditButtonContainer}>
      <Button variant="primary" className={styles.imageEditButton} onClick={() => onViewDetails(restoration.id)}>
        View Details
      </Button>
      {restoration.status === 'completed' && restoration.restored_image_url && onConvertToVideo && (
        <Button
          variant="outline-primary"
          className={styles.imageEditButton}
          onClick={() => onConvertToVideo(restoration.restored_image_url)}
        >
          🎬 Convert to Video
        </Button>
      )}
    </div>
  </div>
  );
};

export default ImageRestoration;
