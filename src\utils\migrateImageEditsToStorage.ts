import { createClient } from '@supabase/supabase-js';

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

/**
 * Convert a base64 data URL to a buffer
 * @param dataUrl - The base64 data URL
 * @returns Buffer containing the image data
 */
function dataUrlToBuffer(dataUrl: string): { buffer: Buffer; mimeType: string } {
  const arr = dataUrl.split(',');
  const mimeType = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return { buffer: Buffer.from(u8arr), mimeType };
}

/**
 * Upload base64 image data to Supabase Storage
 * @param base64Data - The base64 data URL
 * @param userId - The user ID for organizing files
 * @param editId - The edit ID for unique naming
 * @returns Promise<string> - The public URL of the uploaded image
 */
async function uploadBase64ToStorage(
  base64Data: string,
  userId: string,
  editId: string
): Promise<string> {
  try {
    const { buffer, mimeType } = dataUrlToBuffer(base64Data);
    
    // Generate filename
    const extension = mimeType.split('/')[1] || 'jpg';
    const fileName = `image-edits/${userId}/${editId}-original.${extension}`;

    // Upload to Supabase Storage
    const { error } = await supabaseAdmin.storage
      .from('generated-images')
      .upload(fileName, buffer, {
        contentType: mimeType,
        upsert: true // Allow overwrite in case of retry
      });

    if (error) {
      console.error('Storage upload error:', error);
      throw new Error(`Failed to upload image: ${error.message}`);
    }

    // Get public URL
    const { data: urlData } = supabaseAdmin.storage
      .from('generated-images')
      .getPublicUrl(fileName);

    if (!urlData?.publicUrl) {
      throw new Error('Failed to get public URL for uploaded image');
    }

    return urlData.publicUrl;
  } catch (error) {
    console.error('Error uploading base64 to storage:', error);
    throw error;
  }
}

/**
 * Migrate image edits with base64 original_image_url to storage URLs
 * @param batchSize - Number of records to process at once (default: 10)
 * @param dryRun - If true, only log what would be migrated without making changes
 */
export async function migrateImageEditsToStorage(
  batchSize: number = 10,
  dryRun: boolean = false
): Promise<void> {
  try {
    console.log(`Starting migration of image edits to storage (dry run: ${dryRun})`);
    
    // Find all records with base64 original_image_url
    const { data: editsToMigrate, error: fetchError } = await supabaseAdmin
      .from('web_image_edits')
      .select('id, user_id, original_image_url')
      .like('original_image_url', 'data:image/%')
      .limit(batchSize);

    if (fetchError) {
      console.error('Error fetching edits to migrate:', fetchError);
      return;
    }

    if (!editsToMigrate || editsToMigrate.length === 0) {
      console.log('No base64 image edits found to migrate');
      return;
    }

    console.log(`Found ${editsToMigrate.length} image edits to migrate`);

    let successCount = 0;
    let errorCount = 0;

    for (const edit of editsToMigrate) {
      try {
        console.log(`Processing edit ${edit.id}...`);
        
        if (dryRun) {
          console.log(`[DRY RUN] Would migrate edit ${edit.id} for user ${edit.user_id}`);
          successCount++;
          continue;
        }

        // Upload base64 to storage
        const storageUrl = await uploadBase64ToStorage(
          edit.original_image_url,
          edit.user_id,
          edit.id
        );

        // Update the record with the storage URL
        const { error: updateError } = await supabaseAdmin
          .from('web_image_edits')
          .update({
            original_image_url: storageUrl,
            updated_at: new Date().toISOString()
          })
          .eq('id', edit.id);

        if (updateError) {
          console.error(`Error updating edit ${edit.id}:`, updateError);
          errorCount++;
        } else {
          console.log(`Successfully migrated edit ${edit.id}`);
          successCount++;
        }

      } catch (error) {
        console.error(`Error processing edit ${edit.id}:`, error);
        errorCount++;
      }
    }

    console.log(`Migration completed: ${successCount} successful, ${errorCount} errors`);
    
    if (editsToMigrate.length === batchSize) {
      console.log('There may be more records to migrate. Run the function again to continue.');
    }

  } catch (error) {
    console.error('Error during migration:', error);
  }
}

/**
 * Get statistics about image edits storage usage
 */
export async function getImageEditsStorageStats(): Promise<void> {
  try {
    // Count total records
    const { count: totalCount, error: totalError } = await supabaseAdmin
      .from('web_image_edits')
      .select('*', { count: 'exact', head: true });

    if (totalError) {
      console.error('Error counting total records:', totalError);
      return;
    }

    // Count base64 records
    const { count: base64Count, error: base64Error } = await supabaseAdmin
      .from('web_image_edits')
      .select('*', { count: 'exact', head: true })
      .like('original_image_url', 'data:image/%');

    if (base64Error) {
      console.error('Error counting base64 records:', base64Error);
      return;
    }

    // Count storage URL records
    const { count: storageCount, error: storageError } = await supabaseAdmin
      .from('web_image_edits')
      .select('*', { count: 'exact', head: true })
      .like('original_image_url', 'https://%');

    if (storageError) {
      console.error('Error counting storage URL records:', storageError);
      return;
    }

    console.log('Image Edits Storage Statistics:');
    console.log(`Total records: ${totalCount}`);
    console.log(`Base64 records (need migration): ${base64Count}`);
    console.log(`Storage URL records (migrated): ${storageCount}`);
    console.log(`Migration progress: ${storageCount}/${totalCount} (${((storageCount || 0) / (totalCount || 1) * 100).toFixed(1)}%)`);

  } catch (error) {
    console.error('Error getting storage stats:', error);
  }
}
