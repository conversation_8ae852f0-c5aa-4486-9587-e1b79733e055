import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { processImageEditAsync } from '@/utils/imageEditProcessor';

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    
    const { 
      prompt, 
      image_url, 
      token_cost = 10,
      guidance_scale = 3.5,
      safety_tolerance = "2",
      output_format = "jpeg",
      seed 
    } = await request.json();

    // Validate required fields
    if (!prompt || !image_url) {
      return NextResponse.json(
        { error: 'Both prompt and image_url are required' },
        { status: 400 }
      );
    }

    // Check if FAL API key is configured
    if (!process.env.FAL_API_KEY) {
      return NextResponse.json(
        { error: 'FAL API key not configured' },
        { status: 500 }
      );
    }

    // Get user info
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token);
    
    if (authError || !user) {
      console.error('Auth error:', authError);
      return NextResponse.json(
        { error: 'Invalid authentication' },
        { status: 401 }
      );
    }

    // Get user's wallet
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('wallets')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (walletError || !wallet) {
      return NextResponse.json(
        { error: 'User wallet not found' },
        { status: 404 }
      );
    }

    // Check if user has sufficient balance
    if (wallet.balance < token_cost) {
      return NextResponse.json(
        { error: 'Insufficient token balance', required: token_cost, available: wallet.balance },
        { status: 402 }
      );
    }

    // Deduct tokens from wallet
    const { error: deductError } = await supabaseAdmin
      .from('wallets')
      .update({ 
        balance: wallet.balance - token_cost,
        updated_at: new Date().toISOString()
      })
      .eq('id', wallet.id);

    if (deductError) {
      console.error('Error deducting tokens:', deductError);
      return NextResponse.json(
        { error: 'Failed to deduct tokens' },
        { status: 500 }
      );
    }

    // Record token transaction
    const { error: transactionError } = await supabaseAdmin
      .from('token_transactions')
      .insert({
        wallet_id: wallet.id,
        amount: -token_cost,
        description: `Image edit: ${prompt.slice(0, 100)}`,
        transaction_type: 'consumption'
      });

    if (transactionError) {
      console.error('Error recording transaction:', transactionError);
      // Note: We don't fail the request here as the tokens have already been deducted
    }

    // Create record in web_image_edits table
    const { data: editRecord, error: insertError } = await supabaseAdmin
      .from('web_image_edits')
      .insert({
        user_id: user.id,
        wallet_id: wallet.id,
        parent_edit_id: null, // This is a root edit
        root_edit_id: null, // Will be set to the record's own ID after creation
        edit_sequence: 1, // Root edit is always sequence 1
        status: 'pending',
        original_image_url: image_url,
        prompt: prompt,
        guidance_scale: guidance_scale,
        safety_tolerance: safety_tolerance,
        output_format: output_format,
        seed: seed,
        token_cost: token_cost,
        is_public: false
      })
      .select()
      .single();

    if (insertError || !editRecord) {
      console.error('Error creating edit record:', insertError);
      return NextResponse.json(
        { error: 'Failed to create edit record' },
        { status: 500 }
      );
    }

    // Update root_edit_id to point to itself for root edits
    const { error: updateRootError } = await supabaseAdmin
      .from('web_image_edits')
      .update({ root_edit_id: editRecord.id })
      .eq('id', editRecord.id);

    if (updateRootError) {
      console.error('Error updating root_edit_id:', updateRootError);
      // Don't fail the request for this, just log the error
    }

    // Start async processing (don't await)
    processImageEditAsync(editRecord.id).catch(error => {
      console.error('Async processing error:', error);
    });

    // Return the request ID immediately
    return NextResponse.json({
      success: true,
      request_id: editRecord.id,
      status: 'pending'
    });

  } catch (error: any) {
    console.error('Image editing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to start image edit', 
        details: error.message || error.toString() 
      },
      { status: 500 }
    );
  }
} 