#!/usr/bin/env node

/**
 * Migration script to convert base64 image data in web_image_edits to storage URLs
 * 
 * Usage:
 *   node scripts/migrate-image-edits.js --stats          # Show migration statistics
 *   node scripts/migrate-image-edits.js --dry-run        # Show what would be migrated
 *   node scripts/migrate-image-edits.js --migrate        # Actually perform migration
 *   node scripts/migrate-image-edits.js --migrate --batch=20  # Migrate with custom batch size
 */

require('dotenv').config();

const { migrateImageEditsToStorage, getImageEditsStorageStats } = require('../src/utils/migrateImageEditsToStorage.ts');

async function main() {
  const args = process.argv.slice(2);
  const flags = {};
  
  // Parse command line arguments
  args.forEach(arg => {
    if (arg.startsWith('--')) {
      const [key, value] = arg.substring(2).split('=');
      flags[key] = value || true;
    }
  });

  try {
    if (flags.stats) {
      console.log('Getting image edits storage statistics...\n');
      await getImageEditsStorageStats();
    } else if (flags['dry-run']) {
      console.log('Running migration in dry-run mode...\n');
      const batchSize = parseInt(flags.batch) || 10;
      await migrateImageEditsToStorage(batchSize, true);
    } else if (flags.migrate) {
      console.log('Starting actual migration...\n');
      const batchSize = parseInt(flags.batch) || 10;
      
      // Show warning
      console.log('⚠️  WARNING: This will modify your database!');
      console.log('   Make sure you have a backup before proceeding.');
      console.log('   Press Ctrl+C to cancel, or wait 5 seconds to continue...\n');
      
      // Wait 5 seconds
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      await migrateImageEditsToStorage(batchSize, false);
    } else {
      console.log('Image Edits Storage Migration Tool\n');
      console.log('Usage:');
      console.log('  node scripts/migrate-image-edits.js --stats          # Show migration statistics');
      console.log('  node scripts/migrate-image-edits.js --dry-run        # Show what would be migrated');
      console.log('  node scripts/migrate-image-edits.js --migrate        # Actually perform migration');
      console.log('  node scripts/migrate-image-edits.js --migrate --batch=20  # Migrate with custom batch size');
      console.log('\nRecommended workflow:');
      console.log('  1. Run --stats to see current state');
      console.log('  2. Run --dry-run to see what would be migrated');
      console.log('  3. Run --migrate to perform the actual migration');
      console.log('  4. Run --stats again to verify completion');
    }
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

main();
