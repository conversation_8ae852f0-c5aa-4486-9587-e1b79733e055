.container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f7f7f7;
}

.main {
  max-width: 400px;
  width: 100%;
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  color: #333;
}

.form {
  display: flex;
  flex-direction: column;
}

.inputGroup {
  margin-bottom: 1rem;
}

.inputGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #555;
}

.inputGroup input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.submitButton {
  padding: 0.75rem;
  /* background: #0070f3; */
  background: gray;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.submitButton:hover {
  background: #005bb5;
}

.linkText {
  text-align: center;
  margin-top: 1rem;
}

.link {
  color: #0070f3;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
} 