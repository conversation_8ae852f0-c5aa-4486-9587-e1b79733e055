import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    // Fetch all web_image_edits with user info
    const { data: imageEdits, error } = await supabaseAdmin
      .from('web_image_edits')
      .select(`
        id,
        user_id,
        status,
        original_image_url,
        edited_image_url,
        prompt,
        guidance_scale,
        safety_tolerance,
        output_format,
        seed,
        token_cost,
        is_public,
        error_message,
        created_at,
        updated_at,
        completed_at
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching web_image_edits:', error);
      return NextResponse.json({ error: 'Failed to fetch image edits' }, { status: 500 });
    }

    // Fetch user profiles for email/role
    const userIds = [...new Set(imageEdits?.map(i => i.user_id) || [])].filter(Boolean);
    let profiles: any[] = [];
    if (userIds.length > 0) {
      const { data: profileData, error: profilesError } = await supabaseAdmin
        .from('profiles')
        .select('user_id, email, role')
        .in('user_id', userIds);
      if (profilesError) {
        console.error('Error fetching user profiles:', profilesError);
      } else {
        profiles = profileData;
      }
    }
    
    const profilesMap = new Map();
    profiles.forEach((profile: any) => {
      profilesMap.set(profile.user_id, profile);
    });

    const processedImageEdits = imageEdits?.map((row: any) => {
      const userProfile = profilesMap.get(row.user_id);
      return {
        id: row.id,
        user_id: row.user_id,
        user_email: userProfile?.email || 'Unknown',
        user_role: userProfile?.role || 'user',
        status: row.status,
        original_image_url: row.original_image_url,
        edited_image_url: row.edited_image_url,
        prompt: row.prompt,
        guidance_scale: row.guidance_scale,
        safety_tolerance: row.safety_tolerance,
        output_format: row.output_format,
        seed: row.seed,
        token_cost: row.token_cost || 10,
        is_public: row.is_public,
        error_message: row.error_message,
        created_at: row.created_at,
        updated_at: row.updated_at,
        completed_at: row.completed_at
      };
    }) || [];

    // Calculate statistics
    const totalCount = processedImageEdits.length;
    const completedCount = processedImageEdits.filter(img => img.status === 'completed').length;
    const pendingCount = processedImageEdits.filter(img => img.status === 'pending').length;
    const processingCount = processedImageEdits.filter(img => img.status === 'processing').length;
    const failedCount = processedImageEdits.filter(img => img.status === 'failed').length;
    const publicCount = processedImageEdits.filter(img => img.is_public).length;
    const totalTokens = processedImageEdits.reduce((sum, img) => sum + (img.token_cost || 0), 0);

    return NextResponse.json({
      imageEdits: processedImageEdits,
      stats: {
        total_count: totalCount,
        completed_count: completedCount,
        pending_count: pendingCount,
        processing_count: processingCount,
        failed_count: failedCount,
        public_count: publicCount,
        total_tokens: totalTokens
      }
    });
  } catch (error) {
    console.error('Error in admin web_image_edits API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 