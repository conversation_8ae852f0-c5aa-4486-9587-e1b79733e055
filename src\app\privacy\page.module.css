.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
}

.updated {
  color: #6b7280;
  margin: 0.5rem 0 2rem 0;
}

.section {
  margin-bottom: 2rem;
}

.title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.section h2 {
  font-size: 1.25rem;
  margin: 1.25rem 0 0.5rem 0;
}

.section p {
  line-height: 1.7;
  color: #1f2937;
}

.section ul {
  padding-left: 1.25rem;
  margin: 0.5rem 0 0 0;
}

.section li {
  line-height: 1.7;
  color: #1f2937;
}

.footer {
  border-top: 1px solid #e5e7eb;
  padding: 1rem 0;
  margin-top: 2rem;
  background: #fff;
}

.footerContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footerLinksSimple {
  display: flex;
  gap: 0.75rem;
  color: #666;
  font-size: 0.95rem;
}

.footerLinksSimple a {
  color: #4b5563;
  text-decoration: none;
}

.footerLinksSimple a:hover {
  text-decoration: underline;
}

.footerDivider {
  color: #9ca3af;
}




