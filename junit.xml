<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="144" failures="0" errors="0" time="3.998">
  <testsuite name="Web Generations Utils" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.284" tests="10">
    <testcase classname="Web Generations Utils Character Validation validates correct character types" name="Web Generations Utils Character Validation validates correct character types" time="0.009">
    </testcase>
    <testcase classname="Web Generations Utils Character Validation rejects invalid character types" name="Web Generations Utils Character Validation rejects invalid character types" time="0.006">
    </testcase>
    <testcase classname="Web Generations Utils New Token-Based Cost Calculation calculates correct token costs for each step" name="Web Generations Utils New Token-Based Cost Calculation calculates correct token costs for each step" time="0.001">
    </testcase>
    <testcase classname="Web Generations Utils New Token-Based Cost Calculation calculates total token cost" name="Web Generations Utils New Token-Based Cost Calculation calculates total token cost" time="0">
    </testcase>
    <testcase classname="Web Generations Utils New Token-Based Cost Calculation converts tokens to dollars correctly" name="Web Generations Utils New Token-Based Cost Calculation converts tokens to dollars correctly" time="0">
    </testcase>
    <testcase classname="Web Generations Utils Legacy Cost Calculation (Backwards Compatibility) calculates correct cost for all resolutions (now same)" name="Web Generations Utils Legacy Cost Calculation (Backwards Compatibility) calculates correct cost for all resolutions (now same)" time="0.001">
    </testcase>
    <testcase classname="Web Generations Utils Legacy Cost Calculation (Backwards Compatibility) defaults to standard cost for unknown resolutions" name="Web Generations Utils Legacy Cost Calculation (Backwards Compatibility) defaults to standard cost for unknown resolutions" time="0.001">
    </testcase>
    <testcase classname="Web Generations Utils Legacy Cost Calculation (Backwards Compatibility) calculates correct token amounts from dollar amounts" name="Web Generations Utils Legacy Cost Calculation (Backwards Compatibility) calculates correct token amounts from dollar amounts" time="0.001">
    </testcase>
    <testcase classname="Web Generations Utils Integration Tests provides complete cost calculation flow for new characters" name="Web Generations Utils Integration Tests provides complete cost calculation flow for new characters" time="0.001">
    </testcase>
    <testcase classname="Web Generations Utils Integration Tests maintains backwards compatibility for existing flow" name="Web Generations Utils Integration Tests maintains backwards compatibility for existing flow" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="POST /api/admin/free-generations/create" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.234" tests="5">
    <testcase classname="POST /api/admin/free-generations/create should create a free generation link successfully" name="POST /api/admin/free-generations/create should create a free generation link successfully" time="0.016">
    </testcase>
    <testcase classname="POST /api/admin/free-generations/create should return a 500 error if Supabase fails to insert" name="POST /api/admin/free-generations/create should return a 500 error if Supabase fails to insert" time="0.002">
    </testcase>
    <testcase classname="POST /api/admin/free-generations/create should handle unexpected errors gracefully" name="POST /api/admin/free-generations/create should handle unexpected errors gracefully" time="0.001">
    </testcase>
    <testcase classname="POST /api/admin/free-generations/create should use fallback base URL when NEXT_PUBLIC_BASE_URL is not set" name="POST /api/admin/free-generations/create should use fallback base URL when NEXT_PUBLIC_BASE_URL is not set" time="0.002">
    </testcase>
    <testcase classname="POST /api/admin/free-generations/create should use localhost URL in development" name="POST /api/admin/free-generations/create should use localhost URL in development" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Image Storage Utils" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.318" tests="5">
    <testcase classname="Image Storage Utils dataUrlToFile converts base64 to File object" name="Image Storage Utils dataUrlToFile converts base64 to File object" time="0.008">
    </testcase>
    <testcase classname="Image Storage Utils dataUrlToFile handles different mime types" name="Image Storage Utils dataUrlToFile handles different mime types" time="0.001">
    </testcase>
    <testcase classname="Image Storage Utils uploadImageToStorage uploads file and returns URL" name="Image Storage Utils uploadImageToStorage uploads file and returns URL" time="0.021">
    </testcase>
    <testcase classname="Image Storage Utils uploadBase64ToStorage converts and uploads base64" name="Image Storage Utils uploadBase64ToStorage converts and uploads base64" time="0.001">
    </testcase>
    <testcase classname="Image Storage Utils uploadImageToStorage throws error for invalid file" name="Image Storage Utils uploadImageToStorage throws error for invalid file" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="POST /api/free-generations/mortal-kombat/start" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.374" tests="8">
    <testcase classname="POST /api/free-generations/mortal-kombat/start should start a free generation successfully" name="POST /api/free-generations/mortal-kombat/start should start a free generation successfully" time="0.018">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should return 403 for an already used voucher" name="POST /api/free-generations/mortal-kombat/start should return 403 for an already used voucher" time="0.005">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should return 400 if required fields are missing" name="POST /api/free-generations/mortal-kombat/start should return 400 if required fields are missing" time="0.01">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should return 403 for invalid voucher" name="POST /api/free-generations/mortal-kombat/start should return 403 for invalid voucher" time="0.002">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should handle image upload errors gracefully" name="POST /api/free-generations/mortal-kombat/start should handle image upload errors gracefully" time="0.006">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should require resize confirmation for large images" name="POST /api/free-generations/mortal-kombat/start should require resize confirmation for large images" time="0.012">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should process small images normally without resize confirmation" name="POST /api/free-generations/mortal-kombat/start should process small images normally without resize confirmation" time="0.002">
    </testcase>
    <testcase classname="POST /api/free-generations/mortal-kombat/start should handle resize preview upload errors" name="POST /api/free-generations/mortal-kombat/start should handle resize preview upload errors" time="0.003">
    </testcase>
  </testsuite>
  <testsuite name="Mortal Kombat Generation Flow - Integration Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.371" tests="37">
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Character Validation should accept valid character types" name="Mortal Kombat Generation Flow - Integration Tests Character Validation should accept valid character types" time="0.013">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Character Validation should reject invalid character types" name="Mortal Kombat Generation Flow - Integration Tests Character Validation should reject invalid character types" time="0.002">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Character Validation should include all new characters" name="Mortal Kombat Generation Flow - Integration Tests Character Validation should include all new characters" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should calculate correct token costs for each step" name="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should calculate correct token costs for each step" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should calculate total tokens correctly" name="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should calculate total tokens correctly" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should convert tokens to dollars correctly" name="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should convert tokens to dollars correctly" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should have same cost for all video resolutions" name="Mortal Kombat Generation Flow - Integration Tests Updated Token-Based Cost Calculation Logic should have same cost for all video resolutions" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Legacy Cost Calculation Logic (Backwards Compatibility) should calculate correct costs for all resolutions (now equal)" name="Mortal Kombat Generation Flow - Integration Tests Legacy Cost Calculation Logic (Backwards Compatibility) should calculate correct costs for all resolutions (now equal)" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Legacy Cost Calculation Logic (Backwards Compatibility) should convert costs to tokens correctly" name="Mortal Kombat Generation Flow - Integration Tests Legacy Cost Calculation Logic (Backwards Compatibility) should convert costs to tokens correctly" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Legacy Cost Calculation Logic (Backwards Compatibility) should default to standard cost for unknown resolutions" name="Mortal Kombat Generation Flow - Integration Tests Legacy Cost Calculation Logic (Backwards Compatibility) should default to standard cost for unknown resolutions" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Generation Flow States should have valid status states" name="Mortal Kombat Generation Flow - Integration Tests Generation Flow States should have valid status states" time="0.002">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Generation Flow States should have valid step numbers" name="Mortal Kombat Generation Flow - Integration Tests Generation Flow States should have valid step numbers" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Generation Flow States should follow correct step progression" name="Mortal Kombat Generation Flow - Integration Tests Generation Flow States should follow correct step progression" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Video Settings Validation should accept valid durations" name="Mortal Kombat Generation Flow - Integration Tests Video Settings Validation should accept valid durations" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Video Settings Validation should accept valid resolutions" name="Mortal Kombat Generation Flow - Integration Tests Video Settings Validation should accept valid resolutions" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Video Settings Validation should accept valid animation styles" name="Mortal Kombat Generation Flow - Integration Tests Video Settings Validation should accept valid animation styles" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Custom Prompt Functionality should support custom video generation prompts" name="Mortal Kombat Generation Flow - Integration Tests Custom Prompt Functionality should support custom video generation prompts" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Custom Prompt Functionality should have default prompts for all characters" name="Mortal Kombat Generation Flow - Integration Tests Custom Prompt Functionality should have default prompts for all characters" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Custom Prompt Functionality should validate prompt length constraints" name="Mortal Kombat Generation Flow - Integration Tests Custom Prompt Functionality should validate prompt length constraints" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Error Handling Scenarios should handle common HTTP error codes" name="Mortal Kombat Generation Flow - Integration Tests Error Handling Scenarios should handle common HTTP error codes" time="0.008">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Error Handling Scenarios should provide meaningful error messages" name="Mortal Kombat Generation Flow - Integration Tests Error Handling Scenarios should provide meaningful error messages" time="0.022">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Database Schema Validation should have correct request structure" name="Mortal Kombat Generation Flow - Integration Tests Database Schema Validation should have correct request structure" time="0.002">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Database Schema Validation should have correct step structure" name="Mortal Kombat Generation Flow - Integration Tests Database Schema Validation should have correct step structure" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Character Prompt Validation should have prompts for all characters" name="Mortal Kombat Generation Flow - Integration Tests Character Prompt Validation should have prompts for all characters" time="0.002">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Character Prompt Validation should contain character names in prompts" name="Mortal Kombat Generation Flow - Integration Tests Character Prompt Validation should contain character names in prompts" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Character Prompt Validation should have prompts for new characters" name="Mortal Kombat Generation Flow - Integration Tests Character Prompt Validation should have prompts for new characters" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Cancel Functionality should support canceling video generation" name="Mortal Kombat Generation Flow - Integration Tests Cancel Functionality should support canceling video generation" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Cancel Functionality should handle token refunds on cancel" name="Mortal Kombat Generation Flow - Integration Tests Cancel Functionality should handle token refunds on cancel" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should detect images that need resizing" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should detect images that need resizing" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should not resize images within limits" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should not resize images within limits" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should calculate correct scale factors" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should calculate correct scale factors" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should maintain aspect ratios during scaling" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should maintain aspect ratios during scaling" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should handle resize confirmation response structure" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should handle resize confirmation response structure" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should support confirm resize endpoints" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should support confirm resize endpoints" time="0">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should handle supported image formats" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should handle supported image formats" time="0.001">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should preserve EXIF orientation during resize" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should preserve EXIF orientation during resize" time="0.002">
    </testcase>
    <testcase classname="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should validate image quality settings" name="Mortal Kombat Generation Flow - Integration Tests Image Resize Functionality should validate image quality settings" time="0.001">
    </testcase>
  </testsuite>
  <testsuite name="Custom Prompt Functionality" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.387" tests="20">
    <testcase classname="Custom Prompt Functionality Prompt Validation should accept valid custom prompts" name="Custom Prompt Functionality Prompt Validation should accept valid custom prompts" time="0.058">
    </testcase>
    <testcase classname="Custom Prompt Functionality Prompt Validation should reject invalid prompts" name="Custom Prompt Functionality Prompt Validation should reject invalid prompts" time="0.006">
    </testcase>
    <testcase classname="Custom Prompt Functionality Prompt Validation should validate prompt length constraints" name="Custom Prompt Functionality Prompt Validation should validate prompt length constraints" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Default Character Prompts should have default prompts for all characters" name="Custom Prompt Functionality Default Character Prompts should have default prompts for all characters" time="0.005">
    </testcase>
    <testcase classname="Custom Prompt Functionality Default Character Prompts should include character names in default prompts" name="Custom Prompt Functionality Default Character Prompts should include character names in default prompts" time="0.004">
    </testcase>
    <testcase classname="Custom Prompt Functionality Default Character Prompts should include cinematic elements in default prompts" name="Custom Prompt Functionality Default Character Prompts should include cinematic elements in default prompts" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Default Character Prompts should have unique prompts for new characters" name="Custom Prompt Functionality Default Character Prompts should have unique prompts for new characters" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Prompt Processing Logic should handle prompt sanitization" name="Custom Prompt Functionality Prompt Processing Logic should handle prompt sanitization" time="0.002">
    </testcase>
    <testcase classname="Custom Prompt Functionality Prompt Processing Logic should trim whitespace from prompts" name="Custom Prompt Functionality Prompt Processing Logic should trim whitespace from prompts" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Prompt Processing Logic should handle special characters appropriately" name="Custom Prompt Functionality Prompt Processing Logic should handle special characters appropriately" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Custom Prompt UI Integration should support modal interface properties" name="Custom Prompt Functionality Custom Prompt UI Integration should support modal interface properties" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Custom Prompt UI Integration should handle prompt reset functionality" name="Custom Prompt Functionality Custom Prompt UI Integration should handle prompt reset functionality" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Custom Prompt UI Integration should validate textarea constraints" name="Custom Prompt Functionality Custom Prompt UI Integration should validate textarea constraints" time="0">
    </testcase>
    <testcase classname="Custom Prompt Functionality API Integration should include custom prompt in request payload" name="Custom Prompt Functionality API Integration should include custom prompt in request payload" time="0">
    </testcase>
    <testcase classname="Custom Prompt Functionality API Integration should handle missing custom prompt gracefully" name="Custom Prompt Functionality API Integration should handle missing custom prompt gracefully" time="0">
    </testcase>
    <testcase classname="Custom Prompt Functionality API Integration should validate custom prompt in API endpoints" name="Custom Prompt Functionality API Integration should validate custom prompt in API endpoints" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Database Schema Support should support custom prompt in input_data" name="Custom Prompt Functionality Database Schema Support should support custom prompt in input_data" time="0.002">
    </testcase>
    <testcase classname="Custom Prompt Functionality Database Schema Support should handle custom prompt storage and retrieval" name="Custom Prompt Functionality Database Schema Support should handle custom prompt storage and retrieval" time="0.001">
    </testcase>
    <testcase classname="Custom Prompt Functionality Error Handling should handle prompt validation errors" name="Custom Prompt Functionality Error Handling should handle prompt validation errors" time="0">
    </testcase>
    <testcase classname="Custom Prompt Functionality Error Handling should provide helpful error messages" name="Custom Prompt Functionality Error Handling should provide helpful error messages" time="0">
    </testcase>
  </testsuite>
  <testsuite name="POST /api/logo-generation" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.415" tests="4">
    <testcase classname="POST /api/logo-generation Authentication should return 401 when no authorization header is provided" name="POST /api/logo-generation Authentication should return 401 when no authorization header is provided" time="0.01">
    </testcase>
    <testcase classname="POST /api/logo-generation Authentication should return 401 when authorization header is malformed" name="POST /api/logo-generation Authentication should return 401 when authorization header is malformed" time="0.001">
    </testcase>
    <testcase classname="POST /api/logo-generation Basic functionality should handle authentication and admin access requirements" name="POST /api/logo-generation Basic functionality should handle authentication and admin access requirements" time="0.014">
    </testcase>
    <testcase classname="POST /api/logo-generation Environment validation should check for required environment variables" name="POST /api/logo-generation Environment validation should check for required environment variables" time="0.002">
    </testcase>
  </testsuite>
  <testsuite name="ImageEditor Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.731" tests="9">
    <testcase classname="ImageEditor Tests imports the component successfully" name="ImageEditor Tests imports the component successfully" time="0.114">
    </testcase>
    <testcase classname="ImageEditor Tests renders the main interface correctly" name="ImageEditor Tests renders the main interface correctly" time="0.049">
    </testcase>
    <testcase classname="ImageEditor Tests has a file input element" name="ImageEditor Tests has a file input element" time="0.009">
    </testcase>
    <testcase classname="ImageEditor Tests shows edit controls after file upload" name="ImageEditor Tests shows edit controls after file upload" time="0.029">
    </testcase>
    <testcase classname="ImageEditor Tests enables edit button when both image and prompt are provided" name="ImageEditor Tests enables edit button when both image and prompt are provided" time="0.035">
    </testcase>
    <testcase classname="ImageEditor Tests has a working edit button when conditions are met" name="ImageEditor Tests has a working edit button when conditions are met" time="0.02">
    </testcase>
    <testcase classname="ImageEditor Tests displays all necessary interface elements" name="ImageEditor Tests displays all necessary interface elements" time="0.007">
    </testcase>
    <testcase classname="ImageEditor Tests shows auth required error for unauthenticated users" name="ImageEditor Tests shows auth required error for unauthenticated users" time="0.046">
    </testcase>
    <testcase classname="ImageEditor Tests validates file type on upload" name="ImageEditor Tests validates file type on upload" time="0.017">
    </testcase>
  </testsuite>
  <testsuite name="LogoGenerationPage" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.891" tests="15">
    <testcase classname="LogoGenerationPage Admin User Interface should render logo generation form for admin users" name="LogoGenerationPage Admin User Interface should render logo generation form for admin users" time="0.124">
    </testcase>
    <testcase classname="LogoGenerationPage Admin User Interface should render language selector" name="LogoGenerationPage Admin User Interface should render language selector" time="0.021">
    </testcase>
    <testcase classname="LogoGenerationPage Admin User Interface should render all style options" name="LogoGenerationPage Admin User Interface should render all style options" time="0.067">
    </testcase>
    <testcase classname="LogoGenerationPage Admin User Interface should have generate button disabled initially" name="LogoGenerationPage Admin User Interface should have generate button disabled initially" time="0.017">
    </testcase>
    <testcase classname="LogoGenerationPage Language Switching should switch to English when language is changed" name="LogoGenerationPage Language Switching should switch to English when language is changed" time="0.017">
    </testcase>
    <testcase classname="LogoGenerationPage Language Switching should update style button labels when language is changed" name="LogoGenerationPage Language Switching should update style button labels when language is changed" time="0.039">
    </testcase>
    <testcase classname="LogoGenerationPage Form Interaction should enable generate button when prompt is entered" name="LogoGenerationPage Form Interaction should enable generate button when prompt is entered" time="0.015">
    </testcase>
    <testcase classname="LogoGenerationPage Form Interaction should disable generate button when prompt is empty" name="LogoGenerationPage Form Interaction should disable generate button when prompt is empty" time="0.013">
    </testcase>
    <testcase classname="LogoGenerationPage Form Interaction should select style when style button is clicked" name="LogoGenerationPage Form Interaction should select style when style button is clicked" time="0.014">
    </testcase>
    <testcase classname="LogoGenerationPage Form Interaction should change selected style when different style is clicked" name="LogoGenerationPage Form Interaction should change selected style when different style is clicked" time="0.018">
    </testcase>
    <testcase classname="LogoGenerationPage Logo Generation should disable generate button when prompt is empty" name="LogoGenerationPage Logo Generation should disable generate button when prompt is empty" time="0.009">
    </testcase>
    <testcase classname="LogoGenerationPage Logo Generation should enable generate button when prompt is entered" name="LogoGenerationPage Logo Generation should enable generate button when prompt is entered" time="0.014">
    </testcase>
    <testcase classname="LogoGenerationPage Logo Generation should show validation error when trying to generate with empty prompt" name="LogoGenerationPage Logo Generation should show validation error when trying to generate with empty prompt" time="0.015">
    </testcase>
    <testcase classname="LogoGenerationPage Logo Generation should show error message when API call fails" name="LogoGenerationPage Logo Generation should show error message when API call fails" time="0.028">
    </testcase>
    <testcase classname="LogoGenerationPage Error Handling should show error when generation fails" name="LogoGenerationPage Error Handling should show error when generation fails" time="0.033">
    </testcase>
  </testsuite>
  <testsuite name="MortalKombatVideoPage - Simple Tests" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.933" tests="3">
    <testcase classname="MortalKombatVideoPage - Simple Tests renders without crashing" name="MortalKombatVideoPage - Simple Tests renders without crashing" time="0.122">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Simple Tests shows loading state initially" name="MortalKombatVideoPage - Simple Tests shows loading state initially" time="0.048">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Simple Tests has the correct component structure" name="MortalKombatVideoPage - Simple Tests has the correct component structure" time="0.024">
    </testcase>
  </testsuite>
  <testsuite name="FreeGenerationsPage" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.98" tests="3">
    <testcase classname="FreeGenerationsPage renders the page and an empty table correctly" name="FreeGenerationsPage renders the page and an empty table correctly" time="0.098">
    </testcase>
    <testcase classname="FreeGenerationsPage calls the create link API and displays the link on success" name="FreeGenerationsPage calls the create link API and displays the link on success" time="0.138">
    </testcase>
    <testcase classname="FreeGenerationsPage displays an error message when the API call fails" name="FreeGenerationsPage displays an error message when the API call fails" time="0.031">
    </testcase>
  </testsuite>
  <testsuite name="Login Page" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="1.982" tests="11">
    <testcase classname="Login Page renders login form correctly" name="Login Page renders login form correctly" time="0.185">
    </testcase>
    <testcase classname="Login Page submits form with valid credentials successfully" name="Login Page submits form with valid credentials successfully" time="0.055">
    </testcase>
    <testcase classname="Login Page displays error message when login fails" name="Login Page displays error message when login fails" time="0.063">
    </testcase>
    <testcase classname="Login Page clears error message when user starts typing" name="Login Page clears error message when user starts typing" time="0.049">
    </testcase>
    <testcase classname="Login Page handles Google OAuth login successfully" name="Login Page handles Google OAuth login successfully" time="0.016">
    </testcase>
    <testcase classname="Login Page handles Google OAuth login error" name="Login Page handles Google OAuth login error" time="0.033">
    </testcase>
    <testcase classname="Login Page disables form and buttons during submission" name="Login Page disables form and buttons during submission" time="0.163">
    </testcase>
    <testcase classname="Login Page validates required form fields" name="Login Page validates required form fields" time="0.006">
    </testcase>
    <testcase classname="Login Page contains correct placeholders and labels" name="Login Page contains correct placeholders and labels" time="0.007">
    </testcase>
    <testcase classname="Login Page navigates to register page when clicking register link" name="Login Page navigates to register page when clicking register link" time="0.007">
    </testcase>
    <testcase classname="Login Page shows forgot password link when login error occurs" name="Login Page shows forgot password link when login error occurs" time="0.023">
    </testcase>
  </testsuite>
  <testsuite name="MortalKombatVideoPage - Step Navigation Logic" errors="0" failures="0" skipped="2" timestamp="2025-08-29T13:25:07" time="2.001" tests="9">
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic Component Rendering Tests should render step 1 initially when no URL param" name="MortalKombatVideoPage - Step Navigation Logic Component Rendering Tests should render step 1 initially when no URL param" time="0.141">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic Component Rendering Tests should show loading initially" name="MortalKombatVideoPage - Step Navigation Logic Component Rendering Tests should show loading initially" time="0">
      <skipped/>
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic URL Parameter Handling should detect URL parameter presence" name="MortalKombatVideoPage - Step Navigation Logic URL Parameter Handling should detect URL parameter presence" time="0.045">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic URL Parameter Handling should make API call when URL parameter exists" name="MortalKombatVideoPage - Step Navigation Logic URL Parameter Handling should make API call when URL parameter exists" time="0.03">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic Regeneration Logic should validate the regenerate API endpoint path" name="MortalKombatVideoPage - Step Navigation Logic Regeneration Logic should validate the regenerate API endpoint path" time="0.001">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic Regeneration Logic should validate API request format for regeneration" name="MortalKombatVideoPage - Step Navigation Logic Regeneration Logic should validate API request format for regeneration" time="0">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic Regeneration Logic should show new approval interface after regeneration completes" name="MortalKombatVideoPage - Step Navigation Logic Regeneration Logic should show new approval interface after regeneration completes" time="0">
      <skipped/>
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic API Integration should call fetch with correct authentication headers" name="MortalKombatVideoPage - Step Navigation Logic API Integration should call fetch with correct authentication headers" time="0.031">
    </testcase>
    <testcase classname="MortalKombatVideoPage - Step Navigation Logic API Integration should handle API errors gracefully" name="MortalKombatVideoPage - Step Navigation Logic API Integration should handle API errors gracefully" time="0.02">
    </testcase>
  </testsuite>
  <testsuite name="MortalKombatVideoFlow - Image Resize Modal" errors="0" failures="0" skipped="0" timestamp="2025-08-29T13:25:07" time="2.203" tests="5">
    <testcase classname="MortalKombatVideoFlow - Image Resize Modal should show resize modal when API returns requiresResizeConfirmation" name="MortalKombatVideoFlow - Image Resize Modal should show resize modal when API returns requiresResizeConfirmation" time="0.235">
    </testcase>
    <testcase classname="MortalKombatVideoFlow - Image Resize Modal should handle resize confirmation acceptance" name="MortalKombatVideoFlow - Image Resize Modal should handle resize confirmation acceptance" time="0.075">
    </testcase>
    <testcase classname="MortalKombatVideoFlow - Image Resize Modal should handle resize confirmation cancellation" name="MortalKombatVideoFlow - Image Resize Modal should handle resize confirmation cancellation" time="0.066">
    </testcase>
    <testcase classname="MortalKombatVideoFlow - Image Resize Modal should display correct image previews in modal" name="MortalKombatVideoFlow - Image Resize Modal should display correct image previews in modal" time="0.051">
    </testcase>
    <testcase classname="MortalKombatVideoFlow - Image Resize Modal should show loading state when processing resize confirmation" name="MortalKombatVideoFlow - Image Resize Modal should show loading state when processing resize confirmation" time="0.042">
    </testcase>
  </testsuite>
</testsuites>