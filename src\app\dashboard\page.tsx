"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import { supabase } from '../../lib/supabaseClient';
import Header from '@/components/Header';
import Button from 'react-bootstrap/Button';
import styles from './page.module.css';
import ToolCard from '../tools/ToolCard';
import toolCardStyles from '../tools/ToolCard.module.css';
import { useTranslation } from '@/hooks/useTranslation';
import MortalKombatGeneration from './MortalKombatGeneration';
import VideoGeneration from './VideoGeneration';
import ImageEdit from './ImageEdit';
import { logger } from '../../utils/logger';

export default function Dashboard() {
  const router = useRouter();
  const { user } = useSelector((state: RootState) => state.user);
  const [session, setSession] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [chats, setChats] = useState<any[]>([]);
  const [repliesMapping, setRepliesMapping] = useState<Record<string, number>>({});
  const [showEmptyChatWarning, setShowEmptyChatWarning] = useState(false);
  const [showLinkModal, setShowLinkModal] = useState(false);
  const [linkCode, setLinkCode] = useState<string | null>(null);
  const [linkLoading, setLinkLoading] = useState(false);
  const [linkError, setLinkError] = useState<string | null>(null);
  const [walletBalance, setWalletBalance] = useState<number | null>(null);
  const [telegramLinked, setTelegramLinked] = useState<boolean>(false);
  const [mkGenerations, setMkGenerations] = useState<any[]>([]);
  const [videoGenerations, setVideoGenerations] = useState<any[]>([]);
  const [imageEdits, setImageEdits] = useState<any[]>([]);
  const [imageEditsLoading, setImageEditsLoading] = useState(true);
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [showVideoModal, setShowVideoModal] = useState(false);
  const [tools, setTools] = useState<any[]>([]);

  // Always use Romanian translations
  const lang: 'ro' = 'ro';
  const { t } = useTranslation(lang);

  useEffect(() => {
    async function checkSession() {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        // Redirect to login if not authenticated.
        router.push('/login');
      } else {
        setSession(session);
        // Fetch wallet balance and telegram link status
        const { data: wallet, error: walletError } = await supabase
          .from('wallets')
          .select('balance')
          .eq('user_id', session.user.id)
          .maybeSingle();
        if (!walletError && wallet) {
          setWalletBalance(wallet.balance);
        }
        // Check if telegram is linked
        const { data: ext, error: extError } = await supabase
          .from('external_identities')
          .select('telegram_id')
          .eq('user_id', session.user.id)
          .maybeSingle();
        setTelegramLinked(!!(ext && ext.telegram_id));
        // Once the session is available, fetch all chats for this user.
        const { data: chatsData, error } = await supabase
          .from('chats')
          .select('*')
          .eq('user_id', session.user.id)
          .order('start_time', { ascending: false });
        if (error) {
          logger.error('Error fetching chats:', error.message);
        } else {
          setChats(chatsData || []);
        }
        
        setLoading(false);
      }
    }
    checkSession();
  }, [router]);

  // Fetch replies count for the fetched chats.
  useEffect(() => {
    async function fetchRepliesMapping() {
      if (chats.length > 0) {
        const chatIds = chats.map((chat) => chat.id);
        const { data: repliesData, error: repliesError } = await supabase
          .from('chat_replies')
          .select('chat_id')
          .in('chat_id', chatIds);
        if (repliesError) {
          logger.error('Error fetching replies:', repliesError.message);
        } else if (repliesData) {
          const mapping: Record<string, number> = {};
          repliesData.forEach((reply) => {
            const id = reply.chat_id;
            mapping[id] = (mapping[id] || 0) + 1;
          });
          setRepliesMapping(mapping);
        }
      }
    }
    fetchRepliesMapping();
  }, [chats]);

  useEffect(() => {
    async function fetchMkGenerations() {
      if (!session?.user?.id) return;
      const { data, error } = await supabase
        .from('web_generation_requests')
        .select('*')
        .eq('user_id', session.user.id)
        .eq('flow_type', 'mortal_kombat')
        .order('created_at', { ascending: false });
      if (!error && data) setMkGenerations(data);
    }
    fetchMkGenerations();
  }, [session]);

  useEffect(() => {
    async function fetchVideoGenerations() {
      if (!session?.user?.id) return;
      const { data, error } = await supabase
        .from('image_to_video_requests')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false });
      if (!error && data) {
        console.log('Video generations data:', data);
        setVideoGenerations(data);
      } else {
        console.error('Error fetching video generations:', error);
      }
    }
    fetchVideoGenerations();
  }, [session]);

  useEffect(() => {
    async function fetchImageEdits() {
      if (!session?.user?.id) {
        setImageEditsLoading(false);
        return;
      }

      setImageEditsLoading(true);
      const { data, error } = await supabase
        .from('web_image_edits')
        .select('*')
        .eq('user_id', session.user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (!error && data) {
        setImageEdits(data);
      } else {
        console.error('Error fetching image edits:', error);
      }
      setImageEditsLoading(false);
    }
    fetchImageEdits();
  }, [session]);

  useEffect(() => {
    async function fetchTools() {
      const { data, error } = await supabase
        .from('tools')
        .select('*')
        .order('order_index', { ascending: true });
      if (!error && data) setTools(data);
    }
    fetchTools();
  }, []);

  const createChat = async () => {
    // Insert a new chat record for the authenticated user.
    const { data, error } = await supabase
      .from('chats')
      .insert({ user_id: session.user.id })
      .select(); // .select() returns the inserted row(s)

    if (error) {
      logger.error('Error creating chat:', error.message);
      return;
    }

    const chat = data && data[0];
    if (!chat) {
      logger.error('No chat created');
      return;
    }

    // Redirect to the new chat page using the new chat's id.
    router.push(`/chat/${chat.id}`);
  };


  const handleLinkTelegram = async () => {
    setLinkLoading(true);
    setLinkError(null);
    setLinkCode(null);
    try {
      // Generate a random 6-digit code
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();
      // Insert into link_codes table
      const { error } = await supabase.from('link_codes').insert({
        code,
        user_id: session.user.id
      });
      if (error) throw error;
      setLinkCode(code);
      setShowLinkModal(true);
    } catch (err: any) {
      setLinkError('Eroare la generarea codului. Încearcă din nou.');
    } finally {
      setLinkLoading(false);
    }
  };

  const handleUnlinkTelegram = async () => {
    setLinkLoading(true);
    setLinkError(null);
    try {
      // Find the external_identity for this user
      const { data: ext, error: extError } = await supabase
        .from('external_identities')
        .select('id')
        .eq('user_id', session.user.id)
        .maybeSingle();
      if (extError || !ext) throw extError || new Error('No external identity found');
      // Unlink by setting user_id to null
      const { error: updateError } = await supabase
        .from('external_identities')
        .update({ user_id: null })
        .eq('id', ext.id);
      if (updateError) throw updateError;
      setTelegramLinked(false);
    } catch (err: any) {
      setLinkError('Eroare la delink. Încearcă din nou.');
    } finally {
      setLinkLoading(false);
    }
  };

  return (
    <>
      <Header />
      <main className={styles.mainContainer}>
        {loading ? (
          <div>{t('dashboard_loading')}</div>
        ) : (
          <>
            <h1>{t('dashboard_title')}</h1>
            <p>{t('dashboard_welcome', { email: session.user.email })}</p>
            <div style={{ display: 'flex', alignItems: 'center', gap: 24, marginBottom: 24 }}>
              <div style={{ fontSize: 18, fontWeight: 500 }}>
                {t('dashboard_available_credits', { balance: walletBalance !== null ? walletBalance : '...' })}
              </div>
              <Button onClick={() => router.push('/dashboard/generations')} variant="outline-primary">
                {t('dashboard_view_generated_images')}
              </Button>
              <Button onClick={() => router.push('/dashboard/tokens')} variant="outline-success">
                {t('dashboard_credits')}
              </Button>
            </div>

            <div className={toolCardStyles.toolCardRow}>
              {tools.map(tool => {
                const isI2V = tool.route === '/image-to-video';
                const isAdmin = user?.role === 'admin';
                // Use status from Supabase for deactivation
                const isActive = tool.status === 'active';
                const deactivated = isI2V && !isActive;
                return (
                <ToolCard
                  key={tool.id}
                  icon={<span style={{ fontSize: 44 }}>{tool.icon}</span>}
                  title={tool.name}
                  category={tool.category}
                    status={isActive ? 'ACTIV' : 'INACTIV'}
                  description={tool.description}
                  features={tool.features}
                    onLaunch={() => {
                      if (isActive || isAdmin) router.push(tool.route);
                    }}
                  lang="ro"
                  t={key => key === 'launch_tool' ? 'Lansează Unealta' : key}
                    disabled={!isActive && !isAdmin}
                />
                );
              })}
            </div>

            {/* Mortal Kombat Generations Section */}
            <section style={{ margin: '48px 0 0 0' }}>
              <h2 style={{ fontSize: 24, fontWeight: 700, marginBottom: 16 }}>{t('mortal_kombat_generations_section_title')}</h2>
              {mkGenerations.length === 0 ? (
                <div style={{ color: '#888', fontSize: 16 }}>No Mortal Kombat generations found.</div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                  {mkGenerations.slice(0, 5).map(gen => (
                    <MortalKombatGeneration
                      key={gen.id}
                      gen={gen}
                      t={t}
                      onViewDetails={(id) => router.push(`/mortal-kombat-video?id=${id}`)}
                    />
                  ))}
                  {mkGenerations.length > 5 && (
                    <Button variant="secondary" style={{ alignSelf: 'center', marginTop: 16 }} onClick={() => router.push('/dashboard/mk-generations')}>
                      {t('mortal_kombat_generations_view_all')}
                    </Button>
                  )}
                </div>
              )}
            </section>

            {/* Video Generations Section */}
            <section style={{ margin: '48px 0 0 0' }}>
              <h2 style={{ fontSize: 24, fontWeight: 700, marginBottom: 16 }}>{t('video_generations_section_title')}</h2>
              {videoGenerations.length === 0 ? (
                <div style={{ color: '#888', fontSize: 16 }}>{t('video_generations_no_videos')}</div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                  {videoGenerations.slice(0, 5).map(video => (
                    <VideoGeneration
                      key={video.id}
                      video={video}
                      t={t}
                      onViewVideo={(video) => {
                        setSelectedVideo(video);
                        setShowVideoModal(true);
                      }}
                    />
                  ))}
                  {videoGenerations.length > 5 && (
                    <Button variant="secondary" style={{ alignSelf: 'center', marginTop: 16 }} onClick={() => router.push('/dashboard/video-generations')}>
                      {t('video_generations_view_all')}
                    </Button>
                  )}
                </div>
              )}
            </section>

            {/* Image Edits Section */}
            <section style={{ margin: '48px 0 0 0' }}>
              <h2 style={{ fontSize: 24, fontWeight: 700, marginBottom: 16 }}>{t('image_edits_section_title')}</h2>
              {imageEditsLoading ? (
                <div style={{ display: 'flex', alignItems: 'center', gap: 12, color: '#888', fontSize: 16 }}>
                  <div className="spinner" style={{
                    width: 20,
                    height: 20,
                    border: '2px solid #e0e0e0',
                    borderTop: '2px solid #007bff',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Se încarcă editările...
                </div>
              ) : imageEdits.length === 0 ? (
                <div style={{ color: '#888', fontSize: 16 }}>{t('image_edits_no_edits')}</div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: 16 }}>
                  {imageEdits.slice(0, 5).map(edit => (
                    <ImageEdit
                      key={edit.id}
                      edit={edit}
                      t={t}
                      onViewDetails={(id) => router.push(`/image-editor?id=${id}`)}
                    />
                  ))}
                  {imageEdits.length > 5 && (
                    <Button variant="secondary" style={{ alignSelf: 'center', marginTop: 16 }} onClick={() => router.push('/dashboard/image-edits')}>
                      {t('image_edits_view_all')}
                    </Button>
                  )}
                </div>
              )}
            </section>

            {/* Telegram Section - full width below */}
            <div style={{
              margin: '32px 0',
              padding: '32px 24px',
              border: '1px solid #e5e7eb',
              borderRadius: 12,
              background: '#f8fafc',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              width: '100%'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 10 }}>
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 16 16" style={{ color: '#888' }} aria-hidden="true"><path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2zM5 8h6a1 1 0 0 1 1 1v5a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V9a1 1 0 0 1 1-1z"/></svg>
                <span style={{ color: '#888', fontWeight: 500, fontSize: 15 }}>Coming soon</span>
              </div>
              <div style={{ fontSize: 18, fontWeight: 500, color: telegramLinked ? 'green' : 'red', marginBottom: 16 }}>
                {telegramLinked ? 'Telegram este conectat' : 'Telegram nu este conectat'}
              </div>
              <div style={{ fontSize: 16, color: '#333', marginBottom: 20, textAlign: 'center', maxWidth: 600 }}>
                Conectarea cu contul tău de Telegram nu este obligatorie, dar este necesară dacă vrei să alimentezi contul de telegram cu credite.
              </div>
              <Button
                variant="info"
                style={{ color: '#fff', background: '#229ED9', borderColor: '#229ED9', fontSize: 18, padding: '12px 32px' }}
                onClick={() => router.push('/telegram-chat')}
                disabled
              >
                Chat Aivis Telegram
              </Button>
            </div>
         
            {/* All chat-related UI and logic removed. Only dashboard info is shown. */}
          </>
          
        )}
      </main>
      <footer className={styles.footer}>
        <div className={styles.footerContent}>
          <div className={styles.footerLinksSimple}>
            <a href="/privacy">Politica de Confidențialitate</a>
            <span className={styles.footerDivider}>•</span>
            <a href="/terms">Termeni și Condiții</a>
          </div>
        </div>
      </footer>
      {showEmptyChatWarning && (
        <div className={styles.modalBackground} onClick={() => setShowEmptyChatWarning(false)}>
          <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
            <h3 style={{ marginBottom: "1rem" }}>
              You already have an empty conversation
            </h3>
            <p>
              You already have an empty conversation, use that instead of starting a new chat.
            </p>
            <div style={{ display: "flex", justifyContent: "flex-end", marginTop: "1rem" }}>
              <Button variant="secondary" onClick={() => setShowEmptyChatWarning(false)}>
                OK
              </Button>
            </div>
          </div>
        </div>
      )}
      {showLinkModal && (
        <div className={styles.modalBackground} onClick={() => setShowLinkModal(false)}>
          <div className={styles.modalContent} onClick={e => e.stopPropagation()}>
            <h3>Conectează-ți contul de Telegram</h3>
            <p>Trimite acest cod către <b>botul Telegram</b> pentru a-ți conecta contul:</p>
            <div style={{ fontSize: 28, fontWeight: 700, letterSpacing: 2, margin: '1rem 0', color: '#0070f3' }}>{linkCode}</div>
            <a
              href={`https://t.me/aivisbot?text=${linkCode}`}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                display: 'inline-block',
                margin: '0.5rem 0 1rem 0',
                padding: '0.5rem 1.2rem',
                background: '#229ED9',
                color: '#fff',
                borderRadius: 6,
                fontWeight: 500,
                textDecoration: 'none',
                fontSize: 16
              }}
            >
              Trimite codul pe Telegram
            </a>
            <p style={{ fontSize: 14, color: '#555' }}>Deschide Telegram, caută botul și trimite-i acest cod ca mesaj.</p>
            <Button variant="secondary" onClick={() => setShowLinkModal(false)} style={{ marginTop: 16 }}>Închide</Button>
          </div>
        </div>
      )}
      {showVideoModal && selectedVideo && selectedVideo.status === 'completed' && selectedVideo.video_url && (
        <div className={styles.compactModalOverlay} onClick={() => setShowVideoModal(false)}>
          <div className={styles.compactModalContent} onClick={(e) => e.stopPropagation()}>
            <button
              className={styles.compactModalCloseButton}
              onClick={() => setShowVideoModal(false)}
              title="Close"
            >
              ×
            </button>
            <video
              src={selectedVideo.video_url}
              className={styles.compactModalVideo}
              controls
              autoPlay
            />
          </div>
        </div>
      )}
    </>
  );
}