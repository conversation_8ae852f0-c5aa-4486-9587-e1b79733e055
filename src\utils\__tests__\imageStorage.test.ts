/**
 * Tests for image storage utilities
 * Note: These are integration tests that require actual Supabase credentials
 */

import { uploadImageToStorage, dataUrlToFile, uploadBase64ToStorage } from '../imageStorage';

// Mock data for testing
const mockBase64Image = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=';

const mockUserId = 'test-user-123';

describe('Image Storage Utils', () => {
  // Skip tests if no Supabase credentials
  const skipTests = !process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  beforeAll(() => {
    if (skipTests) {
      console.log('Skipping image storage tests - no Supabase credentials');
    }
  });

  test('dataUrlToFile converts base64 to File object', () => {
    const file = dataUrlToFile(mockBase64Image, 'test.jpg');
    
    expect(file).toBeInstanceOf(File);
    expect(file.name).toBe('test.jpg');
    expect(file.type).toBe('image/jpeg');
    expect(file.size).toBeGreaterThan(0);
  });

  test('dataUrlToFile handles different mime types', () => {
    const pngBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==';
    const file = dataUrlToFile(pngBase64, 'test.png');
    
    expect(file.type).toBe('image/png');
  });

  test('uploadImageToStorage uploads file and returns URL', async () => {
    if (skipTests) return;

    const file = dataUrlToFile(mockBase64Image, 'test.jpg');
    
    try {
      const url = await uploadImageToStorage(file, mockUserId, 'test-folder');
      
      expect(url).toMatch(/^https:\/\//);
      expect(url).toContain('generated-images');
      expect(url).toContain('test-folder');
      expect(url).toContain(mockUserId);
    } catch (error) {
      // If this fails due to auth issues, that's expected in CI
      console.log('Upload test failed (expected in CI):', error);
    }
  }, 10000);

  test('uploadBase64ToStorage converts and uploads base64', async () => {
    if (skipTests) return;

    try {
      const url = await uploadBase64ToStorage(mockBase64Image, mockUserId, 'test-folder');
      
      expect(url).toMatch(/^https:\/\//);
      expect(url).toContain('generated-images');
    } catch (error) {
      // If this fails due to auth issues, that's expected in CI
      console.log('Base64 upload test failed (expected in CI):', error);
    }
  }, 10000);

  test('uploadImageToStorage throws error for invalid file', async () => {
    if (skipTests) return;

    const invalidFile = new File(['invalid'], 'test.txt', { type: 'text/plain' });
    
    try {
      await uploadImageToStorage(invalidFile, mockUserId);
      // Should not reach here
      expect(true).toBe(false);
    } catch (error) {
      expect(error).toBeDefined();
    }
  });
});
