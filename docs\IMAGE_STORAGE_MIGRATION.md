# Image Storage Migration

## Problem
The `web_image_edits` table was storing original images as base64 data URLs in the `original_image_url` column, causing:
- Massive database response sizes (38MB+ for single queries)
- Poor performance when loading image edit data
- Inefficient storage usage

## Solution
Migrated from base64 storage to Supabase Storage URLs:

### 1. Updated Image Editor Frontend
**File:** `src/app/image-editor/page.tsx`
- Replaced `uploadImageToFal()` function with `uploadImageToStorage()`
- Now uploads images directly to Supabase Storage bucket `generated-images`
- Returns public URLs instead of base64 data

### 2. Created Storage Utilities
**File:** `src/utils/imageStorage.ts`
- `uploadImageToStorage()` - Upload File objects to storage
- `uploadBase64ToStorage()` - Convert and upload base64 data
- `dataUrlToFile()` - Convert base64 to File objects

### 3. Migration Tools
**File:** `src/utils/migrateImageEditsToStorage.ts`
- `migrateImageEditsToStorage()` - Batch migrate existing base64 records
- `getImageEditsStorageStats()` - Show migration progress statistics

**File:** `scripts/migrate-image-edits.js`
- Command-line tool for running migrations
- Supports dry-run mode and batch processing

**File:** `src/app/api/admin/migrate-image-edits/route.ts`
- Admin API endpoint for web-based migration
- GET: Returns migration statistics
- POST: Runs migration with progress tracking

### 4. Testing
**File:** `src/utils/__tests__/imageStorage.test.ts`
- Unit tests for storage utility functions
- Integration tests for actual uploads (when credentials available)

## Storage Structure
Images are stored in the `generated-images` bucket with the following path structure:
```
image-edits/{user_id}/{timestamp}-{random_id}.{extension}
```

## Migration Process

### Option 1: Command Line
```bash
# Check current status
node scripts/migrate-image-edits.js --stats

# Test migration (dry run)
node scripts/migrate-image-edits.js --dry-run

# Run actual migration
node scripts/migrate-image-edits.js --migrate

# Run with custom batch size
node scripts/migrate-image-edits.js --migrate --batch=20
```

### Option 2: Admin API
```bash
# Get statistics
curl -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  https://your-app.com/api/admin/migrate-image-edits

# Run migration (dry run)
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 10, "dryRun": true}' \
  https://your-app.com/api/admin/migrate-image-edits

# Run actual migration
curl -X POST -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"batchSize": 10, "dryRun": false}' \
  https://your-app.com/api/admin/migrate-image-edits
```

## Backward Compatibility
The system maintains backward compatibility:
- Existing base64 records continue to work with FAL API
- New uploads automatically use storage URLs
- Migration can be run incrementally without downtime

## Performance Impact
**Before:**
- Single image edit query: ~38MB response
- Database storage: ~2MB per base64 image

**After:**
- Single image edit query: ~2KB response (99.99% reduction)
- Storage: Efficient file storage with CDN delivery
- Faster page loads and API responses

## Security Considerations
- Images stored in public bucket for FAL API access
- File paths include user ID for organization
- Unique timestamps and random IDs prevent conflicts
- Admin endpoints require proper authentication

## Monitoring
Use the statistics functions to monitor migration progress:
- Total records vs migrated records
- Percentage completion
- Error tracking during migration

## Next Steps
1. Run migration on production data
2. Monitor performance improvements
3. Consider cleanup of old base64 data after successful migration
4. Update any other systems that might reference the old format
