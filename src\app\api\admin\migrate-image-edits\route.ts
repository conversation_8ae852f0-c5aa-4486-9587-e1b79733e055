import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { migrateImageEditsToStorage, getImageEditsStorageStats } from '@/utils/migrateImageEditsToStorage';

// Create Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  { auth: { autoRefreshToken: false, persistSession: false } }
);

// Simple admin authentication check
async function isAdmin(request: NextRequest): Promise<boolean> {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader) return false;

  const token = authHeader.replace('Bearer ', '');
  
  try {
    const { data: { user }, error } = await supabaseAdmin.auth.getUser(token);
    if (error || !user) return false;

    // Check if user is admin (you can customize this logic)
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    return profile?.role === 'admin';
  } catch {
    return false;
  }
}

// GET endpoint to get migration statistics
export async function GET(request: NextRequest) {
  try {
    if (!(await isAdmin(request))) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Capture console output
    const logs: string[] = [];
    const originalLog = console.log;
    console.log = (...args) => {
      logs.push(args.join(' '));
      originalLog(...args);
    };

    try {
      await getImageEditsStorageStats();
    } finally {
      console.log = originalLog;
    }

    // Parse the logs to extract statistics
    const statsLog = logs.join('\n');
    const totalMatch = statsLog.match(/Total records: (\d+)/);
    const base64Match = statsLog.match(/Base64 records \(need migration\): (\d+)/);
    const storageMatch = statsLog.match(/Storage URL records \(migrated\): (\d+)/);
    const progressMatch = statsLog.match(/Migration progress: \d+\/\d+ \((\d+\.\d+)%\)/);

    return NextResponse.json({
      success: true,
      stats: {
        total: totalMatch ? parseInt(totalMatch[1]) : 0,
        needsMigration: base64Match ? parseInt(base64Match[1]) : 0,
        migrated: storageMatch ? parseInt(storageMatch[1]) : 0,
        progressPercent: progressMatch ? parseFloat(progressMatch[1]) : 0
      },
      logs: statsLog
    });

  } catch (error: any) {
    console.error('Error getting migration stats:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get migration statistics',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// POST endpoint to run migration
export async function POST(request: NextRequest) {
  try {
    if (!(await isAdmin(request))) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { batchSize = 10, dryRun = false } = await request.json();

    // Capture console output
    const logs: string[] = [];
    const originalLog = console.log;
    const originalError = console.error;
    
    console.log = (...args) => {
      logs.push(`[LOG] ${args.join(' ')}`);
      originalLog(...args);
    };
    
    console.error = (...args) => {
      logs.push(`[ERROR] ${args.join(' ')}`);
      originalError(...args);
    };

    let success = true;
    let error = null;

    try {
      await migrateImageEditsToStorage(batchSize, dryRun);
    } catch (err: any) {
      success = false;
      error = err.message;
    } finally {
      console.log = originalLog;
      console.error = originalError;
    }

    // Parse results from logs
    const logsText = logs.join('\n');
    const foundMatch = logsText.match(/Found (\d+) image edits to migrate/);
    const completedMatch = logsText.match(/Migration completed: (\d+) successful, (\d+) errors/);
    const moreRecordsMatch = logsText.includes('There may be more records to migrate');

    return NextResponse.json({
      success,
      error,
      results: {
        found: foundMatch ? parseInt(foundMatch[1]) : 0,
        successful: completedMatch ? parseInt(completedMatch[1]) : 0,
        errors: completedMatch ? parseInt(completedMatch[2]) : 0,
        hasMoreRecords: moreRecordsMatch,
        dryRun
      },
      logs: logsText
    });

  } catch (error: any) {
    console.error('Error running migration:', error);
    return NextResponse.json(
      { 
        error: 'Failed to run migration',
        details: error.message 
      },
      { status: 500 }
    );
  }
}
