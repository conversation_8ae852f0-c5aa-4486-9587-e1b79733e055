import React from 'react'
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import userSlice from '../../../store/userSlice'

// Mock translation hook with Romanian translations
jest.mock('../../../hooks/useTranslation', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'image_editor_title': 'Editor de Imagini',
        'image_editor_subtitle': 'Încarcă o imagine și folosește AI pentru a o edita cu prompturi text',
        'image_editor_upload_title': 'Încarcă o Imagine',
        'image_editor_upload_description': 'Trage și lasă o imagine aici, fă clic pentru a selecta',
        'image_editor_supported_formats': 'Suportate: JPEG, PNG, WebP',
        'image_editor_prompt_placeholder': '<PERSON><PERSON><PERSON> schimbările pe care vrei să le faci...',
        'image_editor_settings': '<PERSON><PERSON><PERSON>',
        'image_editor_edit_button': 'Editează Imaginea',
        'image_editor_reset': 'Resetează',
        'image_editor_status_processing': 'Se procesează editarea imaginii...',
        'image_editor_error_file_type': 'Selectează un fișier de imagine valid',
        'image_editor_error_edit_image': 'Editarea imaginii a eșuat',
        'image_editor_paste_tip': 'Sfat: Poți lipi imagini direct cu Ctrl+V',
        'image_editor_error': 'Eroare',
        'image_editor_error_auth_required': 'Autentificare necesară. Conectează-te pentru a edita imagini.',
        'image_editor_prompt_label': 'Prompt de Editare',
        'image_editor_click_to_replace': 'Clic pentru a înlocui',
        'image_editor_status_pending': 'Cererea ta de editare a fost trimisă și așteaptă să fie procesată...',
        'image_editor_error_no_image_prompt': 'Selectează o imagine și furnizează un prompt',
        'image_editor_status_failed': 'Editare eșuată',
      }
      return translations[key] || key
    }
  })
}))

// Mock Next.js hooks
const mockPush = jest.fn()
const mockGet = jest.fn()
jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: mockPush }),
  useSearchParams: () => ({ get: mockGet }),
}))

// Mock Supabase client
const mockGetSession = jest.fn()
jest.mock('../../../lib/supabaseClient', () => ({
  supabase: {
    auth: {
      getSession: mockGetSession,
    },
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({
            data: null,
            error: null
          })
        })
      })
    })
  }
}))

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: { user: userSlice },
    preloadedState: { 
      user: { 
        user: { id: 'test', email: '<EMAIL>', role: 'user', tokens: 100 }, 
        loading: false, 
        error: null, 
        ...initialState 
      } 
    },
  })
}

// Helper function to create mock files
const createMockFile = (name = 'test.jpg', type = 'image/jpeg') => {
  return new File(['test content'], name, { type })
}

// Mock Redux
jest.mock('react-redux', () => ({
  ...jest.requireActual('react-redux'),
  useSelector: () => ({ user: { id: 'test', email: '<EMAIL>', role: 'user', tokens: 100 }, loading: false }),
  useDispatch: () => jest.fn(),
}))

// Mock Supabase
jest.mock('../../../lib/supabaseClient', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({ data: { session: null } }),
    },
  },
}))

// Mock Header component
jest.mock('../../../components/Header', () => {
  return function MockHeader() {
    return <div data-testid="header">Header</div>
  }
})

// Mock CSS modules
jest.mock('../../admin/admin.module.css', () => ({}))

// Mock logger
jest.mock('../../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
  },
}))

describe('ImageEditor Tests', () => {
  beforeEach(() => {
    global.fetch = jest.fn()
    jest.clearAllMocks()
    mockPush.mockClear()
    mockGet.mockClear()
    mockGetSession.mockClear()
    // Set default mock for getSession to avoid auth errors
    mockGetSession.mockResolvedValue({
      data: { 
        session: { 
          access_token: 'mock-token',
          user: { id: 'test-user' }
        } 
      }
    })
  })

  it('imports the component successfully', () => {
    const ImageEditor = require('../page').default
    expect(typeof ImageEditor).toBe('function')
  })

  it('renders the main interface correctly', () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    // Check main elements with Romanian translations
    expect(screen.getByText('Editor de Imagini')).toBeTruthy()
    expect(screen.getByText('Încarcă o imagine și folosește AI pentru a o edita cu prompturi text')).toBeTruthy()
    expect(screen.getByText('Încarcă o Imagine')).toBeTruthy()
    expect(screen.getByText('Trage și lasă o imagine aici, fă clic pentru a selecta')).toBeTruthy()
    expect(screen.getByText('Suportate: JPEG, PNG, WebP')).toBeTruthy()
  })

  it('has a file input element', () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    const fileInput = document.querySelector('input[type="file"]')
    expect(fileInput).toBeTruthy()
    expect(fileInput?.getAttribute('accept')).toBe('image/*')
  })

  it('shows edit controls after file upload', async () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    const mockFile = createMockFile()
    
    await act(async () => {
      fireEvent.change(fileInput, { target: { files: [mockFile] } })
    })
    
    await waitFor(() => {
      const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
      expect(promptInput).toBeTruthy()
      expect(screen.getByText('Editează Imaginea')).toBeTruthy()
    })
  })

  it('enables edit button when both image and prompt are provided', async () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    const mockFile = createMockFile()
    
    await act(async () => {
      fireEvent.change(fileInput, { target: { files: [mockFile] } })
    })
    
    await waitFor(() => {
      const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
      expect(promptInput).toBeTruthy()
    })
    
    const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
    
    act(() => {
      fireEvent.change(promptInput, { target: { value: 'make it blue' } })
    })
    
    const editButton = screen.getByText('Editează Imaginea')
    expect(editButton).toBeTruthy()
    expect(editButton).not.toBeDisabled()
  })

  it('has a working edit button when conditions are met', async () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    const mockFile = createMockFile()
    
    await act(async () => {
      fireEvent.change(fileInput, { target: { files: [mockFile] } })
    })
    
    await waitFor(() => {
      const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
      expect(promptInput).toBeTruthy()
    })
    
    const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
    
    act(() => {
      fireEvent.change(promptInput, { target: { value: 'make it blue' } })
    })
    
    const editButton = screen.getByText('Editează Imaginea')
    expect(editButton).toBeTruthy()
    
    // Button should be clickable (not disabled)
    expect(editButton).not.toBeDisabled()
  })

  it('displays all necessary interface elements', () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    // Check for title and description
    expect(screen.getByText('Editor de Imagini')).toBeTruthy()
    expect(screen.getByText('Încarcă o imagine și folosește AI pentru a o edita cu prompturi text')).toBeTruthy()
    
    // Check for upload area
    expect(screen.getByText('Încarcă o Imagine')).toBeTruthy()
    expect(screen.getByText('Trage și lasă o imagine aici, fă clic pentru a selecta')).toBeTruthy()
    expect(screen.getByText('Suportate: JPEG, PNG, WebP')).toBeTruthy()
    
    // Check for tip
    expect(screen.getByText('Sfat: Poți lipi imagini direct cu Ctrl+V')).toBeTruthy()
  })

  it('shows auth required error for unauthenticated users', async () => {
    // Mock no session
    mockGetSession.mockResolvedValue({
      data: { session: null }
    })
    
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    const mockFile = createMockFile()
    
    await act(async () => {
      fireEvent.change(fileInput, { target: { files: [mockFile] } })
    })
    
    await waitFor(() => {
      const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
      expect(promptInput).toBeTruthy()
    })
    
    const promptInput = screen.getByPlaceholderText('Descrie schimbările pe care vrei să le faci...')
    
    act(() => {
      fireEvent.change(promptInput, { target: { value: 'make it blue' } })
    })
    
    const editButton = screen.getByText('Editează Imaginea')
    
    act(() => {
      fireEvent.click(editButton)
    })
    
    // Should show auth error when trying to edit without session
    await waitFor(() => {
      expect(screen.getByText(/Autentificare necesară. Conectează-te pentru a edita imagini./)).toBeTruthy()
    })
  })

  it('validates file type on upload', async () => {
    const ImageEditor = require('../page').default
    const store = createMockStore()
    render(
      <Provider store={store}>
        <ImageEditor />
      </Provider>
    )
    
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    const invalidFile = createMockFile('test.txt', 'text/plain')
    
    act(() => {
      fireEvent.change(fileInput, { target: { files: [invalidFile] } })
    })
    
    await waitFor(() => {
      expect(screen.getByText('Selectează un fișier de imagine valid')).toBeTruthy()
    })
  })
}) 