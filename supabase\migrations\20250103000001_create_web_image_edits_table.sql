-- Create web_image_edits table
CREATE TABLE web_image_edits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES wallets(id) ON DELETE CASCADE,
  
  -- Request details
  status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  original_image_url TEXT NOT NULL,
  prompt TEXT NOT NULL,
  
  -- Settings
  guidance_scale DECIMAL(3, 1) DEFAULT 3.5,
  safety_tolerance VARCHAR(2) DEFAULT '2',
  output_format VARCHAR(10) DEFAULT 'jpeg' CHECK (output_format IN ('jpeg', 'png')),
  seed INTEGER,
  
  -- Generated content
  edited_image_url TEXT,
  fal_request_id TEXT,
  
  -- Cost tracking
  token_cost INTEGER DEFAULT 10,
  
  -- Public visibility
  is_public BOOLEAN DEFAULT FALSE,
  
  -- Error handling
  error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_web_image_edits_user_id ON web_image_edits(user_id);
CREATE INDEX idx_web_image_edits_status ON web_image_edits(status);
CREATE INDEX idx_web_image_edits_created_at ON web_image_edits(created_at);
CREATE INDEX idx_web_image_edits_is_public ON web_image_edits(is_public);

-- Create updated_at trigger
CREATE TRIGGER update_web_image_edits_updated_at
  BEFORE UPDATE ON web_image_edits
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE web_image_edits ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own image edits"
  ON web_image_edits
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own image edits"
  ON web_image_edits
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own image edits"
  ON web_image_edits
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Public edits can be viewed by anyone
CREATE POLICY "Anyone can view public image edits"
  ON web_image_edits
  FOR SELECT
  USING (is_public = TRUE);

-- Admin policies
CREATE POLICY "Admins can view all image edits"
  ON web_image_edits
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.user_metadata->>'role' = 'admin'
    )
  );

CREATE POLICY "Admins can update all image edits"
  ON web_image_edits
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM auth.users 
      WHERE auth.users.id = auth.uid() 
      AND auth.users.user_metadata->>'role' = 'admin'
    )
  );

-- Service role can do everything (for API operations)
CREATE POLICY "Service role can manage image edits"
  ON web_image_edits
  FOR ALL
  USING (current_setting('role') = 'service_role'); 