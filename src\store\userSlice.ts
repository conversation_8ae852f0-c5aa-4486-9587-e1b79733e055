import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { supabase } from '../lib/supabaseClient';
import { logger } from '../utils/logger';

interface UserProfile {
  id: string;
  email: string;
  role: string;
  tokens: number;
}

interface UserState {
  user: UserProfile | null;
  loading: boolean;
  error: string | null;
}

const initialState: UserState = {
  user: null,
  loading: false,
  error: null,
};

// Async thunk to fetch the user profile from auth and profiles table.
export const fetchUserProfile = createAsyncThunk<UserProfile | null>(
  'user/fetchProfile',
  async (_, thunkAPI) => {
    logger.log('Fetching user profile');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    if (sessionError) {
      return thunkAPI.rejectWithValue(sessionError.message);
    }
    if (session && session.user) {
      const user = session.user;
      // Fetch the profile data from the profiles table.
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();
      if (profileError) {
        return thunkAPI.rejectWithValue(profileError.message);
      }

      // Fetch wallet balance from wallets table
      const { data: walletData, error: walletError } = await supabase
        .from('wallets')
        .select('balance')
        .eq('user_id', user.id)
        .single();

      // Use wallet balance if available, otherwise fall back to profile tokens
      const tokens = walletError ? profileData.tokens : walletData.balance;

      return {
        id: user.id,
        email: user.email || '',
        role: profileData.role,
        tokens: tokens,
      };
    }
    return null;
  }
);

export const logout = createAsyncThunk(
  'user/logout',
  async (_, thunkAPI) => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
    return null;
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearUser(state) {
      state.user = null;
    },
    // You can add more synchronous actions here if needed.
  },
  extraReducers: (builder) => {
    builder.addCase(fetchUserProfile.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchUserProfile.fulfilled, (state, action) => {
      state.loading = false;
      state.user = action.payload;
    });
    builder.addCase(fetchUserProfile.rejected, (state, action) => {
      state.loading = false;
      state.error = action.payload as string;
    });
    builder.addCase(logout.fulfilled, (state) => {
      state.user = null;
      state.error = null;
    });
  },
});

export const { clearUser } = userSlice.actions;
export default userSlice.reducer; 